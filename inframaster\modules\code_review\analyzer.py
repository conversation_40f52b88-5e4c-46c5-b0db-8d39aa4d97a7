"""
Code analyzer for the code review module.

Provides static analysis capabilities for different programming languages
and generates insights for the review process.
"""

import re
from typing import Dict, List, Optional, Set, Tuple

from inframaster.core.github.models import <PERSON>Change, PullRequest
from inframaster.core.logging import get_logger
from inframaster.modules.code_review.models import (
    CodeIssue,
    FileAnalysis,
    ReviewCategory,
    ReviewSeverity,
)

logger = get_logger(__name__)


class CodeAnalyzer:
    """Analyzes code changes for potential issues and metrics."""
    
    # File extensions to programming languages mapping
    LANGUAGE_EXTENSIONS = {
        ".py": "python",
        ".js": "javascript",
        ".ts": "typescript",
        ".jsx": "javascript",
        ".tsx": "typescript",
        ".java": "java",
        ".kt": "kotlin",
        ".go": "go",
        ".rs": "rust",
        ".cpp": "cpp",
        ".cc": "cpp",
        ".cxx": "cpp",
        ".c": "c",
        ".h": "c",
        ".hpp": "cpp",
        ".cs": "csharp",
        ".rb": "ruby",
        ".php": "php",
        ".swift": "swift",
        ".scala": "scala",
        ".sh": "shell",
        ".bash": "shell",
        ".zsh": "shell",
        ".sql": "sql",
        ".yaml": "yaml",
        ".yml": "yaml",
        ".json": "json",
        ".xml": "xml",
        ".html": "html",
        ".css": "css",
        ".scss": "scss",
        ".sass": "sass",
        ".md": "markdown",
        ".dockerfile": "dockerfile",
        ".tf": "terraform",
    }
    
    # Generated file patterns
    GENERATED_FILE_PATTERNS = [
        r".*\.generated\.",
        r".*\.gen\.",
        r".*_pb2\.py$",
        r".*\.pb\.go$",
        r".*/node_modules/.*",
        r".*/vendor/.*",
        r".*/build/.*",
        r".*/dist/.*",
        r".*/target/.*",
        r".*\.min\.js$",
        r".*\.min\.css$",
        r".*package-lock\.json$",
        r".*yarn\.lock$",
        r".*Cargo\.lock$",
        r".*Gemfile\.lock$",
    ]
    
    def __init__(self):
        """Initialize the code analyzer."""
        self.compiled_patterns = [
            re.compile(pattern) for pattern in self.GENERATED_FILE_PATTERNS
        ]
        logger.info("Initialized code analyzer")
    
    def analyze_file_change(self, file_change: FileChange) -> FileAnalysis:
        """
        Analyze a single file change.
        
        Args:
            file_change: File change to analyze
            
        Returns:
            FileAnalysis with detected issues and metrics
        """
        language = self._detect_language(file_change.filename)
        
        analysis = FileAnalysis(
            file_path=file_change.filename,
            language=language,
            lines_added=file_change.additions,
            lines_removed=file_change.deletions,
        )
        
        # Skip analysis for generated files
        if self._is_generated_file(file_change.filename):
            logger.debug(f"Skipping generated file: {file_change.filename}")
            return analysis
        
        # Analyze the patch if available
        if file_change.patch:
            issues = self._analyze_patch(file_change.patch, language)
            analysis.issues.extend(issues)
            
            # Calculate complexity score
            analysis.complexity_score = self._calculate_complexity(file_change.patch, language)
            
            # Add file-level metrics
            analysis.metrics = self._calculate_metrics(file_change.patch, language)
        
        # Generate summary
        analysis.summary = self._generate_file_summary(analysis)
        
        logger.debug(f"Analyzed {file_change.filename}: {len(analysis.issues)} issues found")
        return analysis
    
    def _detect_language(self, filename: str) -> Optional[str]:
        """Detect programming language from filename."""
        for ext, lang in self.LANGUAGE_EXTENSIONS.items():
            if filename.lower().endswith(ext):
                return lang
        return None
    
    def _is_generated_file(self, filename: str) -> bool:
        """Check if a file is likely generated."""
        return any(pattern.match(filename) for pattern in self.compiled_patterns)
    
    def _analyze_patch(self, patch: str, language: Optional[str]) -> List[CodeIssue]:
        """Analyze a patch for potential issues."""
        issues = []
        
        # Split patch into lines
        lines = patch.split('\n')
        current_line = 0
        
        for i, line in enumerate(lines):
            # Track line numbers (simplified)
            if line.startswith('@@'):
                # Parse line number from hunk header
                match = re.search(r'\+(\d+)', line)
                if match:
                    current_line = int(match.group(1))
                continue
            
            if line.startswith('+') and not line.startswith('+++'):
                # This is an added line
                added_line = line[1:]  # Remove the '+' prefix
                line_issues = self._analyze_line(added_line, current_line, language)
                issues.extend(line_issues)
                current_line += 1
            elif not line.startswith('-') and not line.startswith('\\'):
                # Context line
                current_line += 1
        
        return issues
    
    def _analyze_line(self, line: str, line_number: int, language: Optional[str]) -> List[CodeIssue]:
        """Analyze a single line of code."""
        issues = []
        
        # Common issues across languages
        issues.extend(self._check_common_issues(line, line_number))
        
        # Language-specific checks
        if language == "python":
            issues.extend(self._check_python_issues(line, line_number))
        elif language in ["javascript", "typescript"]:
            issues.extend(self._check_javascript_issues(line, line_number))
        elif language == "java":
            issues.extend(self._check_java_issues(line, line_number))
        
        return issues
    
    def _check_common_issues(self, line: str, line_number: int) -> List[CodeIssue]:
        """Check for common issues across all languages."""
        issues = []
        
        # Long lines
        if len(line) > 120:
            issues.append(CodeIssue(
                file_path="",  # Will be set by caller
                line_number=line_number,
                severity=ReviewSeverity.MINOR,
                category=ReviewCategory.STYLE,
                title="Line too long",
                description=f"Line exceeds 120 characters ({len(line)} characters)",
                suggestion="Consider breaking this line into multiple lines for better readability",
                confidence=0.9,
            ))
        
        # Trailing whitespace
        if line.rstrip() != line:
            issues.append(CodeIssue(
                file_path="",
                line_number=line_number,
                severity=ReviewSeverity.MINOR,
                category=ReviewCategory.STYLE,
                title="Trailing whitespace",
                description="Line contains trailing whitespace",
                suggestion="Remove trailing whitespace",
                confidence=0.95,
            ))
        
        # TODO comments
        if re.search(r'\b(TODO|FIXME|HACK|XXX)\b', line, re.IGNORECASE):
            issues.append(CodeIssue(
                file_path="",
                line_number=line_number,
                severity=ReviewSeverity.INFO,
                category=ReviewCategory.MAINTAINABILITY,
                title="TODO comment found",
                description="Code contains a TODO/FIXME comment",
                suggestion="Consider creating a ticket to track this work",
                confidence=0.8,
            ))
        
        # Hardcoded secrets (basic patterns)
        secret_patterns = [
            (r'password\s*=\s*["\'][^"\']+["\']', "Hardcoded password"),
            (r'api[_-]?key\s*=\s*["\'][^"\']+["\']', "Hardcoded API key"),
            (r'secret\s*=\s*["\'][^"\']+["\']', "Hardcoded secret"),
            (r'token\s*=\s*["\'][^"\']+["\']', "Hardcoded token"),
        ]
        
        for pattern, description in secret_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                issues.append(CodeIssue(
                    file_path="",
                    line_number=line_number,
                    severity=ReviewSeverity.CRITICAL,
                    category=ReviewCategory.SECURITY,
                    title="Potential hardcoded secret",
                    description=description,
                    suggestion="Use environment variables or secure configuration management",
                    confidence=0.7,
                ))
        
        return issues
    
    def _check_python_issues(self, line: str, line_number: int) -> List[CodeIssue]:
        """Check for Python-specific issues."""
        issues = []
        
        # Import issues
        if line.strip().startswith('from') and '*' in line:
            issues.append(CodeIssue(
                file_path="",
                line_number=line_number,
                severity=ReviewSeverity.MINOR,
                category=ReviewCategory.BEST_PRACTICES,
                title="Wildcard import",
                description="Using wildcard imports can pollute the namespace",
                suggestion="Import specific names instead of using '*'",
                confidence=0.8,
            ))
        
        # Print statements (should use logging)
        if re.search(r'\bprint\s*\(', line):
            issues.append(CodeIssue(
                file_path="",
                line_number=line_number,
                severity=ReviewSeverity.MINOR,
                category=ReviewCategory.BEST_PRACTICES,
                title="Print statement found",
                description="Using print() for output in production code",
                suggestion="Consider using logging instead of print statements",
                confidence=0.7,
            ))
        
        # Bare except clauses
        if re.search(r'except\s*:', line):
            issues.append(CodeIssue(
                file_path="",
                line_number=line_number,
                severity=ReviewSeverity.MAJOR,
                category=ReviewCategory.BUGS,
                title="Bare except clause",
                description="Catching all exceptions can hide bugs",
                suggestion="Specify the exception type(s) to catch",
                confidence=0.9,
            ))
        
        return issues
    
    def _check_javascript_issues(self, line: str, line_number: int) -> List[CodeIssue]:
        """Check for JavaScript/TypeScript-specific issues."""
        issues = []
        
        # Console.log statements
        if re.search(r'\bconsole\.(log|debug|info|warn|error)\s*\(', line):
            issues.append(CodeIssue(
                file_path="",
                line_number=line_number,
                severity=ReviewSeverity.MINOR,
                category=ReviewCategory.BEST_PRACTICES,
                title="Console statement found",
                description="Console statements should not be in production code",
                suggestion="Remove console statements or use proper logging",
                confidence=0.8,
            ))
        
        # == instead of ===
        if re.search(r'[^=!]==[^=]', line):
            issues.append(CodeIssue(
                file_path="",
                line_number=line_number,
                severity=ReviewSeverity.MINOR,
                category=ReviewCategory.BEST_PRACTICES,
                title="Loose equality comparison",
                description="Using == instead of === can lead to unexpected behavior",
                suggestion="Use strict equality (===) instead of loose equality (==)",
                confidence=0.8,
            ))
        
        return issues
    
    def _check_java_issues(self, line: str, line_number: int) -> List[CodeIssue]:
        """Check for Java-specific issues."""
        issues = []
        
        # System.out.println
        if re.search(r'System\.out\.(print|println)', line):
            issues.append(CodeIssue(
                file_path="",
                line_number=line_number,
                severity=ReviewSeverity.MINOR,
                category=ReviewCategory.BEST_PRACTICES,
                title="System.out usage found",
                description="Using System.out for logging in production code",
                suggestion="Use a proper logging framework instead",
                confidence=0.8,
            ))
        
        return issues
    
    def _calculate_complexity(self, patch: str, language: Optional[str]) -> float:
        """Calculate complexity score for the patch."""
        # Simple complexity calculation based on various factors
        complexity = 0.0
        
        lines = patch.split('\n')
        added_lines = [line for line in lines if line.startswith('+') and not line.startswith('+++')]
        
        if not added_lines:
            return 0.0
        
        # Base complexity from number of lines
        complexity += len(added_lines) * 0.1
        
        # Increase complexity for control structures
        control_patterns = [
            r'\b(if|else|elif|while|for|switch|case|try|catch|finally)\b',
            r'\b(function|def|class|interface)\b',
        ]
        
        for line in added_lines:
            for pattern in control_patterns:
                complexity += len(re.findall(pattern, line, re.IGNORECASE)) * 0.5
        
        # Normalize to 0-10 scale
        return min(complexity / 10.0, 10.0)
    
    def _calculate_metrics(self, patch: str, language: Optional[str]) -> Dict[str, float]:
        """Calculate various code metrics."""
        lines = patch.split('\n')
        added_lines = [line[1:] for line in lines if line.startswith('+') and not line.startswith('+++')]
        
        if not added_lines:
            return {}
        
        # Calculate basic metrics
        total_lines = len(added_lines)
        non_empty_lines = len([line for line in added_lines if line.strip()])
        comment_lines = len([line for line in added_lines if line.strip().startswith('#') or line.strip().startswith('//')])
        
        return {
            "total_lines": total_lines,
            "non_empty_lines": non_empty_lines,
            "comment_lines": comment_lines,
            "comment_ratio": comment_lines / max(non_empty_lines, 1),
            "avg_line_length": sum(len(line) for line in added_lines) / max(total_lines, 1),
        }
    
    def _generate_file_summary(self, analysis: FileAnalysis) -> str:
        """Generate a summary for the file analysis."""
        if not analysis.issues:
            return f"No issues found in {analysis.file_path}"
        
        issue_counts = {}
        for issue in analysis.issues:
            issue_counts[issue.severity] = issue_counts.get(issue.severity, 0) + 1
        
        summary_parts = []
        for severity in [ReviewSeverity.CRITICAL, ReviewSeverity.MAJOR, ReviewSeverity.MINOR, ReviewSeverity.INFO]:
            count = issue_counts.get(severity, 0)
            if count > 0:
                summary_parts.append(f"{count} {severity.value}")
        
        return f"Found {len(analysis.issues)} issues: {', '.join(summary_parts)}"
