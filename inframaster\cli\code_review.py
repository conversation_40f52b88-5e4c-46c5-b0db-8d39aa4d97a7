"""
CLI commands for the code review module.

Provides command-line interface for AI-powered code review functionality.
"""

import asyncio
import json
import sys
from typing import Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

from inframaster.core.config import LLMProvider, Settings
from inframaster.modules.code_review.agent import CodeReviewAgent
from inframaster.modules.code_review.models import ReviewConfig, ReviewSeverity

console = Console()


@click.group()
def code_review_cli():
    """AI-powered code review commands."""
    pass


@code_review_cli.command(name="review")
@click.argument("pr_url")
@click.option(
    "--dry-run",
    is_flag=True,
    help="Perform review without posting comments to GitHub",
)
@click.option(
    "--llm-provider",
    type=click.Choice([provider.value for provider in LLMProvider]),
    help="Override LLM provider for this review",
)
@click.option(
    "--max-files",
    type=int,
    default=50,
    help="Maximum number of files to review",
)
@click.option(
    "--focus",
    type=click.Choice(["security", "performance", "maintainability", "style", "all"]),
    default="all",
    help="Focus area for the review",
)
@click.option(
    "--output",
    type=click.Choice(["table", "json", "summary"]),
    default="table",
    help="Output format",
)
@click.option(
    "--post-comments/--no-post-comments",
    default=None,
    help="Override config setting for posting comments",
)
@click.pass_context
def review_pr(
    ctx: click.Context,
    pr_url: str,
    dry_run: bool,
    llm_provider: Optional[str],
    max_files: int,
    focus: str,
    output: str,
    post_comments: Optional[bool],
):
    """
    Review a GitHub pull request using AI.
    
    PR_URL should be a GitHub pull request URL like:
    https://github.com/owner/repo/pull/123
    """
    
    async def run_review():
        settings: Settings = ctx.obj["settings"]
        
        # Override LLM provider if specified
        if llm_provider:
            settings.llm_provider = LLMProvider(llm_provider)
        
        # Create review config
        config = ReviewConfig(
            max_files_to_review=max_files,
            post_comments=post_comments if post_comments is not None else settings.code_review_post_comments,
        )
        
        # Set focus areas
        if focus != "all":
            config.focus_on_security = focus == "security"
            config.focus_on_performance = focus == "performance"
            config.focus_on_maintainability = focus == "maintainability"
            config.focus_on_style = focus == "style"
        
        # Initialize agent
        agent = CodeReviewAgent(settings, config)
        
        # Show progress
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            task = progress.add_task("Starting code review...", total=None)
            
            try:
                # Update progress
                progress.update(task, description="Retrieving pull request...")
                
                # Perform review
                result = await agent.review_pr(
                    pr_url=pr_url,
                    dry_run=dry_run,
                    post_comments=post_comments,
                )
                
                progress.update(task, description="Review completed!")
                
                # Display results
                if output == "table":
                    display_review_table(result)
                elif output == "json":
                    display_review_json(result)
                elif output == "summary":
                    display_review_summary(result)
                
                # Show dry run notice
                if dry_run:
                    console.print("\n[yellow]Note: This was a dry run. No comments were posted to GitHub.[/yellow]")
                
                return result
                
            except Exception as e:
                progress.update(task, description=f"Error: {e}")
                console.print(f"\n[red]Error during code review: {e}[/red]")
                sys.exit(1)
    
    # Run the async function
    asyncio.run(run_review())


@code_review_cli.command(name="config")
@click.option(
    "--show",
    is_flag=True,
    help="Show current configuration",
)
@click.option(
    "--set",
    "set_option",
    type=(str, str),
    multiple=True,
    help="Set configuration option (key value)",
)
@click.pass_context
def configure(ctx: click.Context, show: bool, set_option: tuple):
    """Configure code review settings."""
    
    if show:
        settings: Settings = ctx.obj["settings"]
        
        table = Table(title="Code Review Configuration")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Max Files to Review", str(settings.code_review_max_files))
        table.add_row("Max Diff Size", str(settings.code_review_max_diff_size))
        table.add_row("Post Comments", str(settings.code_review_post_comments))
        
        console.print(table)
    
    if set_option:
        console.print("[yellow]Configuration setting is not yet implemented.[/yellow]")
        console.print("Please edit your .env file or use environment variables.")


def display_review_table(result):
    """Display review result as a table."""
    
    # Summary panel
    summary_text = result.summary or "No summary available"
    if result.overall_score is not None:
        summary_text += f"\n\nOverall Score: {result.overall_score:.1f}/10"
    if result.recommendation:
        summary_text += f"\nRecommendation: {result.recommendation.replace('_', ' ').title()}"
    
    console.print(Panel(summary_text, title="Review Summary", border_style="blue"))
    
    # Statistics table
    stats_table = Table(title="Review Statistics")
    stats_table.add_column("Metric", style="cyan")
    stats_table.add_column("Value", style="green")
    
    stats_table.add_row("Files Analyzed", str(len(result.files_analyzed)))
    stats_table.add_row("Total Issues", str(result.total_issues))
    stats_table.add_row("Processing Time", f"{result.processing_time:.2f}s")
    stats_table.add_row("Model Used", f"{result.reviewer_provider} ({result.reviewer_model})")
    
    console.print(stats_table)
    
    # Issues by severity
    if result.issues_by_severity:
        severity_table = Table(title="Issues by Severity")
        severity_table.add_column("Severity", style="cyan")
        severity_table.add_column("Count", style="green")
        
        for severity in [ReviewSeverity.CRITICAL, ReviewSeverity.MAJOR, ReviewSeverity.MINOR, ReviewSeverity.INFO]:
            count = result.issues_by_severity.get(severity, 0)
            if count > 0:
                color = {
                    ReviewSeverity.CRITICAL: "red",
                    ReviewSeverity.MAJOR: "yellow", 
                    ReviewSeverity.MINOR: "blue",
                    ReviewSeverity.INFO: "green",
                }[severity]
                severity_table.add_row(
                    severity.value.title(),
                    f"[{color}]{count}[/{color}]"
                )
        
        console.print(severity_table)
    
    # File details
    if result.files_analyzed:
        files_table = Table(title="Files Analyzed")
        files_table.add_column("File", style="cyan")
        files_table.add_column("Language", style="green")
        files_table.add_column("Issues", style="yellow")
        files_table.add_column("Summary", style="white")
        
        for file_analysis in result.files_analyzed:
            files_table.add_row(
                file_analysis.file_path,
                file_analysis.language or "Unknown",
                str(len(file_analysis.issues)),
                file_analysis.summary or "No issues found"
            )
        
        console.print(files_table)


def display_review_json(result):
    """Display review result as JSON."""
    
    # Convert to JSON-serializable format
    data = {
        "pr_url": result.pr_url,
        "pr_number": result.pr_number,
        "repository": result.repository,
        "reviewed_at": result.reviewed_at.isoformat(),
        "reviewer_model": result.reviewer_model,
        "reviewer_provider": result.reviewer_provider,
        "total_issues": result.total_issues,
        "overall_score": result.overall_score,
        "recommendation": result.recommendation,
        "summary": result.summary,
        "processing_time": result.processing_time,
        "issues_by_severity": {
            severity.value: count 
            for severity, count in result.issues_by_severity.items()
        },
        "files_analyzed": [
            {
                "file_path": file_analysis.file_path,
                "language": file_analysis.language,
                "lines_added": file_analysis.lines_added,
                "lines_removed": file_analysis.lines_removed,
                "complexity_score": file_analysis.complexity_score,
                "issues": [
                    {
                        "line_number": issue.line_number,
                        "severity": issue.severity.value,
                        "category": issue.category.value,
                        "title": issue.title,
                        "description": issue.description,
                        "suggestion": issue.suggestion,
                        "confidence": issue.confidence,
                    }
                    for issue in file_analysis.issues
                ],
                "summary": file_analysis.summary,
            }
            for file_analysis in result.files_analyzed
        ],
    }
    
    console.print(json.dumps(data, indent=2))


def display_review_summary(result):
    """Display a concise review summary."""
    
    # Title
    console.print(f"\n[bold]Code Review Results for PR #{result.pr_number}[/bold]")
    console.print(f"Repository: {result.repository}")
    
    # Key metrics
    console.print(f"\n[cyan]Files Analyzed:[/cyan] {len(result.files_analyzed)}")
    console.print(f"[cyan]Total Issues:[/cyan] {result.total_issues}")
    
    if result.overall_score is not None:
        score_color = "green" if result.overall_score >= 8 else "yellow" if result.overall_score >= 6 else "red"
        console.print(f"[cyan]Overall Score:[/cyan] [{score_color}]{result.overall_score:.1f}/10[/{score_color}]")
    
    if result.recommendation:
        rec_color = "green" if result.recommendation == "approve" else "red" if result.recommendation == "request_changes" else "yellow"
        rec_text = result.recommendation.replace("_", " ").title()
        console.print(f"[cyan]Recommendation:[/cyan] [{rec_color}]{rec_text}[/{rec_color}]")
    
    # Critical issues
    critical_issues = result.critical_issues
    if critical_issues:
        console.print(f"\n[red]⚠️  {len(critical_issues)} Critical Issues Found:[/red]")
        for issue in critical_issues[:5]:  # Show first 5
            console.print(f"  • {issue.title} ({issue.file_path}:{issue.line_number})")
        if len(critical_issues) > 5:
            console.print(f"  ... and {len(critical_issues) - 5} more")
    
    # Summary
    if result.summary:
        console.print(f"\n[bold]Summary:[/bold]")
        console.print(result.summary)
    
    console.print(f"\n[dim]Review completed in {result.processing_time:.2f}s using {result.reviewer_provider}[/dim]")
