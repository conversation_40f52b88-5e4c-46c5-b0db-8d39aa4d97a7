"""
GitHub API client implementation.

Provides a high-level interface for interacting with GitHub's REST API
with proper error handling, rate limiting, and authentication.
"""

import re
from datetime import datetime
from typing import List, Optional, Tuple
from urllib.parse import urlparse

import httpx
from github import Github
from github.GithubException import GithubException, RateLimitExceededException
from tenacity import retry, stop_after_attempt, wait_exponential

from inframaster.core.github.models import (
    FileChange,
    GitHubError,
    GitHubRepository,
    GitHubUser,
    PullRequest,
    RateLimitError,
    Review,
    ReviewComment,
)
from inframaster.core.logging import get_logger

logger = get_logger(__name__)


class GitHubClient:
    """GitHub API client with rate limiting and error handling."""
    
    def __init__(self, token: str, base_url: str = "https://api.github.com"):
        """
        Initialize GitHub client.
        
        Args:
            token: GitHub personal access token
            base_url: GitHub API base URL
        """
        self.token = token
        self.base_url = base_url
        
        # Initialize PyGithub client
        self.github = Github(token, base_url=base_url)
        
        # Initialize httpx client for direct API calls
        self.http_client = httpx.AsyncClient(
            headers={
                "Authorization": f"token {token}",
                "Accept": "application/vnd.github.v3+json",
                "User-Agent": "InfraMaster/1.0",
            },
            timeout=30.0,
        )
        
        logger.info("Initialized GitHub client")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.http_client.aclose()
    
    def parse_pr_url(self, pr_url: str) -> Tuple[str, str, int]:
        """
        Parse a GitHub PR URL to extract owner, repo, and PR number.
        
        Args:
            pr_url: GitHub PR URL
            
        Returns:
            Tuple of (owner, repo, pr_number)
            
        Raises:
            ValueError: If URL format is invalid
        """
        # Support various GitHub URL formats
        patterns = [
            r"github\.com/([^/]+)/([^/]+)/pull/(\d+)",
            r"api\.github\.com/repos/([^/]+)/([^/]+)/pulls/(\d+)",
        ]
        
        for pattern in patterns:
            match = re.search(pattern, pr_url)
            if match:
                owner, repo, pr_number = match.groups()
                return owner, repo, int(pr_number)
        
        raise ValueError(f"Invalid GitHub PR URL format: {pr_url}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True,
    )
    async def get_pull_request(self, owner: str, repo: str, pr_number: int) -> PullRequest:
        """
        Get pull request information.
        
        Args:
            owner: Repository owner
            repo: Repository name
            pr_number: Pull request number
            
        Returns:
            PullRequest object with complete information
            
        Raises:
            GitHubError: If API request fails
            RateLimitError: If rate limit is exceeded
        """
        try:
            # Get PR using PyGithub
            repository = self.github.get_repo(f"{owner}/{repo}")
            pr = repository.get_pull(pr_number)
            
            # Convert to our model
            pull_request = PullRequest(
                id=pr.id,
                number=pr.number,
                title=pr.title,
                body=pr.body,
                state=pr.state,
                html_url=pr.html_url,
                diff_url=pr.diff_url,
                patch_url=pr.patch_url,
                user=GitHubUser(
                    login=pr.user.login,
                    id=pr.user.id,
                    avatar_url=pr.user.avatar_url,
                    html_url=pr.user.html_url,
                    type=pr.user.type,
                ),
                base_repo=GitHubRepository(
                    id=pr.base.repo.id,
                    name=pr.base.repo.name,
                    full_name=pr.base.repo.full_name,
                    owner=GitHubUser(
                        login=pr.base.repo.owner.login,
                        id=pr.base.repo.owner.id,
                        avatar_url=pr.base.repo.owner.avatar_url,
                        html_url=pr.base.repo.owner.html_url,
                        type=pr.base.repo.owner.type,
                    ),
                    html_url=pr.base.repo.html_url,
                    clone_url=pr.base.repo.clone_url,
                    default_branch=pr.base.repo.default_branch,
                    private=pr.base.repo.private,
                ),
                head_repo=GitHubRepository(
                    id=pr.head.repo.id,
                    name=pr.head.repo.name,
                    full_name=pr.head.repo.full_name,
                    owner=GitHubUser(
                        login=pr.head.repo.owner.login,
                        id=pr.head.repo.owner.id,
                        avatar_url=pr.head.repo.owner.avatar_url,
                        html_url=pr.head.repo.owner.html_url,
                        type=pr.head.repo.owner.type,
                    ),
                    html_url=pr.head.repo.html_url,
                    clone_url=pr.head.repo.clone_url,
                    default_branch=pr.head.repo.default_branch,
                    private=pr.head.repo.private,
                ),
                base_ref=pr.base.ref,
                head_ref=pr.head.ref,
                base_sha=pr.base.sha,
                head_sha=pr.head.sha,
                created_at=pr.created_at,
                updated_at=pr.updated_at,
                merged_at=pr.merged_at,
                closed_at=pr.closed_at,
                additions=pr.additions,
                deletions=pr.deletions,
                changed_files=pr.changed_files,
                commits=pr.commits,
                labels=[label.name for label in pr.labels],
                draft=pr.draft,
                mergeable=pr.mergeable,
                mergeable_state=pr.mergeable_state,
            )
            
            # Get file changes
            files = pr.get_files()
            pull_request.files = [
                FileChange(
                    filename=file.filename,
                    status=file.status,
                    additions=file.additions,
                    deletions=file.deletions,
                    changes=file.changes,
                    patch=file.patch,
                    previous_filename=getattr(file, "previous_filename", None),
                )
                for file in files
            ]
            
            logger.info(f"Retrieved PR #{pr_number} from {owner}/{repo}")
            return pull_request
            
        except RateLimitExceededException as e:
            reset_time = datetime.fromtimestamp(e.rate_limit.reset.timestamp())
            raise RateLimitError(reset_time)
        except GithubException as e:
            raise GitHubError(f"GitHub API error: {e.data.get('message', str(e))}", e.status)
        except Exception as e:
            raise GitHubError(f"Unexpected error: {str(e)}")
    
    async def post_review_comment(
        self,
        owner: str,
        repo: str,
        pr_number: int,
        comment: ReviewComment,
    ) -> ReviewComment:
        """
        Post a review comment on a pull request.
        
        Args:
            owner: Repository owner
            repo: Repository name
            pr_number: Pull request number
            comment: Review comment to post
            
        Returns:
            Posted ReviewComment with ID and metadata
            
        Raises:
            GitHubError: If API request fails
        """
        try:
            repository = self.github.get_repo(f"{owner}/{repo}")
            pr = repository.get_pull(pr_number)
            
            # Post the comment
            github_comment = pr.create_review_comment(
                body=comment.body,
                commit=pr.head.sha,
                path=comment.path,
                line=comment.line,
                side=comment.side,
            )
            
            # Return updated comment
            posted_comment = ReviewComment(
                id=github_comment.id,
                body=github_comment.body,
                path=github_comment.path,
                line=github_comment.line,
                side=comment.side,
                user=GitHubUser(
                    login=github_comment.user.login,
                    id=github_comment.user.id,
                    avatar_url=github_comment.user.avatar_url,
                    html_url=github_comment.user.html_url,
                    type=github_comment.user.type,
                ),
                created_at=github_comment.created_at,
                updated_at=github_comment.updated_at,
                html_url=github_comment.html_url,
                commit_id=pr.head.sha,
            )
            
            logger.info(f"Posted review comment on PR #{pr_number}")
            return posted_comment
            
        except GithubException as e:
            raise GitHubError(f"Failed to post comment: {e.data.get('message', str(e))}", e.status)
        except Exception as e:
            raise GitHubError(f"Unexpected error posting comment: {str(e)}")
    
    async def create_review(
        self,
        owner: str,
        repo: str,
        pr_number: int,
        review: Review,
    ) -> Review:
        """
        Create a pull request review.
        
        Args:
            owner: Repository owner
            repo: Repository name
            pr_number: Pull request number
            review: Review to create
            
        Returns:
            Created Review with ID and metadata
            
        Raises:
            GitHubError: If API request fails
        """
        try:
            repository = self.github.get_repo(f"{owner}/{repo}")
            pr = repository.get_pull(pr_number)
            
            # Prepare review comments
            comments = []
            for comment in review.comments:
                comments.append({
                    "path": comment.path,
                    "line": comment.line,
                    "body": comment.body,
                    "side": comment.side,
                })
            
            # Create the review
            github_review = pr.create_review(
                body=review.body,
                event=review.state,
                comments=comments,
            )
            
            # Return created review
            created_review = Review(
                id=github_review.id,
                body=github_review.body,
                state=github_review.state,
                html_url=github_review.html_url,
                submitted_at=github_review.submitted_at,
                commit_id=pr.head.sha,
                user=GitHubUser(
                    login=github_review.user.login,
                    id=github_review.user.id,
                    avatar_url=github_review.user.avatar_url,
                    html_url=github_review.user.html_url,
                    type=github_review.user.type,
                ),
                comments=review.comments,  # Keep original comments
            )
            
            logger.info(f"Created review on PR #{pr_number}")
            return created_review
            
        except GithubException as e:
            raise GitHubError(f"Failed to create review: {e.data.get('message', str(e))}", e.status)
        except Exception as e:
            raise GitHubError(f"Unexpected error creating review: {str(e)}")
