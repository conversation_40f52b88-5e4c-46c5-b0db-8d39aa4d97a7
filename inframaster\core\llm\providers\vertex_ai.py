"""
Vertex AI (Google Cloud) LLM provider implementation.

Provides integration with Google's Vertex AI platform and Gemini models.
"""

import os
from typing import List, Optional

from langchain_google_vertexai import ChatVertexAI
from google.cloud import aiplatform

from inframaster.core.llm.base import <PERSON><PERSON><PERSON><PERSON>ider, LLMMessage, LLMResponse
from inframaster.core.logging import get_logger

logger = get_logger(__name__)


class VertexAIProvider(BaseLLMProvider):
    """Vertex AI LLM provider using Google's Gemini models."""
    
    def __init__(
        self,
        project_id: str,
        location: str = "us-central1",
        model_name: str = "gemini-1.5-pro",
        credentials_path: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize Vertex AI provider.
        
        Args:
            project_id: Google Cloud project ID
            location: Vertex AI location/region
            model_name: Gemini model to use
            credentials_path: Path to service account credentials
            **kwargs: Additional configuration
        """
        super().__init__(**kwargs)
        
        self.project_id = project_id
        self.location = location
        self._model_name = model_name
        self.credentials_path = credentials_path
        
        # Set up authentication if credentials path is provided
        if credentials_path:
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = credentials_path
        
        # Initialize Vertex AI
        aiplatform.init(project=project_id, location=location)
        
        # Initialize the LangChain Vertex AI client
        self.client = ChatVertexAI(
            model_name=model_name,
            project=project_id,
            location=location,
            temperature=0.7,
            max_output_tokens=8192,
        )
        
        logger.info(f"Initialized Vertex AI provider with model: {model_name}")
    
    @property
    def provider_name(self) -> str:
        """Return the provider name."""
        return "vertex_ai"
    
    @property
    def model_name(self) -> str:
        """Return the model name."""
        return self._model_name
    
    async def generate(
        self,
        messages: List[LLMMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Generate a response using Vertex AI.
        
        Args:
            messages: List of conversation messages
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Returns:
            LLMResponse with generated content
        """
        try:
            # Update client parameters
            self.client.temperature = temperature
            if max_tokens:
                self.client.max_output_tokens = max_tokens
            
            # Convert messages to LangChain format
            langchain_messages = self._convert_to_langchain_messages(messages)
            
            # Generate response
            response = await self.client.ainvoke(langchain_messages)
            
            return LLMResponse(
                content=response.content,
                model=self._model_name,
                provider=self.provider_name,
                usage=getattr(response, "usage_metadata", None),
                metadata={
                    "project_id": self.project_id,
                    "location": self.location,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                }
            )
            
        except Exception as e:
            logger.error(f"Error generating response with Vertex AI: {e}")
            raise
    
    async def generate_stream(
        self,
        messages: List[LLMMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ):
        """
        Generate a streaming response using Vertex AI.
        
        Args:
            messages: List of conversation messages
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Yields:
            Partial LLMResponse objects
        """
        try:
            # Update client parameters
            self.client.temperature = temperature
            if max_tokens:
                self.client.max_output_tokens = max_tokens
            
            # Convert messages to LangChain format
            langchain_messages = self._convert_to_langchain_messages(messages)
            
            # Generate streaming response
            accumulated_content = ""
            async for chunk in self.client.astream(langchain_messages):
                if chunk.content:
                    accumulated_content += chunk.content
                    yield LLMResponse(
                        content=accumulated_content,
                        model=self._model_name,
                        provider=self.provider_name,
                        metadata={
                            "streaming": True,
                            "project_id": self.project_id,
                            "location": self.location,
                        }
                    )
                    
        except Exception as e:
            logger.error(f"Error generating streaming response with Vertex AI: {e}")
            raise
    
    async def validate_connection(self) -> bool:
        """
        Validate connection to Vertex AI.
        
        Returns:
            True if connection is successful
        """
        try:
            # Test with a simple message
            test_messages = [
                LLMMessage(role="user", content="Hello, can you respond with 'OK'?")
            ]
            response = await self.generate(test_messages, max_tokens=10)
            return bool(response.content)
            
        except Exception as e:
            logger.error(f"Vertex AI connection validation failed: {e}")
            return False
    
    def _convert_to_langchain_messages(self, messages: List[LLMMessage]):
        """Convert LLMMessage objects to LangChain message format."""
        from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
        
        langchain_messages = []
        for msg in messages:
            if msg.role == "system":
                langchain_messages.append(SystemMessage(content=msg.content))
            elif msg.role == "user":
                langchain_messages.append(HumanMessage(content=msg.content))
            elif msg.role == "assistant":
                langchain_messages.append(AIMessage(content=msg.content))
            else:
                # Default to human message for unknown roles
                langchain_messages.append(HumanMessage(content=msg.content))
        
        return langchain_messages
