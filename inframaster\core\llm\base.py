"""
Base classes and interfaces for LLM providers.

Defines the common interface that all LLM providers must implement.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel


@dataclass
class LLMResponse:
    """Response from an LLM provider."""
    content: str
    model: str
    provider: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class LLMMessage(BaseModel):
    """A message in an LLM conversation."""
    role: str  # "system", "user", "assistant"
    content: str
    
    class Config:
        frozen = True


class BaseLLMProvider(ABC):
    """
    Abstract base class for all LLM providers.
    
    This class defines the interface that all LLM providers must implement
    to ensure consistent behavior across different AI services.
    """
    
    def __init__(self, **kwargs):
        """Initialize the LLM provider with configuration."""
        self.config = kwargs
    
    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Return the name of this provider."""
        pass
    
    @property
    @abstractmethod
    def model_name(self) -> str:
        """Return the model name being used."""
        pass
    
    @abstractmethod
    async def generate(
        self,
        messages: List[LLMMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Generate a response from the LLM.
        
        Args:
            messages: List of messages in the conversation
            temperature: Sampling temperature (0.0 to 1.0)
            max_tokens: Maximum number of tokens to generate
            **kwargs: Additional provider-specific parameters
            
        Returns:
            LLMResponse containing the generated content and metadata
        """
        pass
    
    @abstractmethod
    async def generate_stream(
        self,
        messages: List[LLMMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ):
        """
        Generate a streaming response from the LLM.
        
        Args:
            messages: List of messages in the conversation
            temperature: Sampling temperature (0.0 to 1.0)
            max_tokens: Maximum number of tokens to generate
            **kwargs: Additional provider-specific parameters
            
        Yields:
            Partial LLMResponse objects as the response is generated
        """
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """
        Validate that the provider can connect to the LLM service.
        
        Returns:
            True if connection is successful, False otherwise
        """
        pass
    
    def format_messages_for_provider(self, messages: List[LLMMessage]) -> Any:
        """
        Format messages for the specific provider's API.
        
        This method can be overridden by providers that need special
        message formatting.
        
        Args:
            messages: List of LLMMessage objects
            
        Returns:
            Provider-specific message format
        """
        return [{"role": msg.role, "content": msg.content} for msg in messages]
