"""
Main CLI entry point for InfraMaster.

Provides the main command-line interface with subcommands
for different modules and functionality.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.table import Table

from inframaster.core.config import <PERSON><PERSON><PERSON>ider, Settings, get_settings
from inframaster.core.logging import setup_logging
from inframaster.cli.code_review import code_review_cli

console = Console()


@click.group()
@click.option(
    "--config",
    type=click.Path(exists=True, path_type=Path),
    help="Path to configuration file",
)
@click.option(
    "--debug",
    is_flag=True,
    help="Enable debug logging",
)
@click.option(
    "--llm-provider",
    type=click.Choice([provider.value for provider in LLMProvider]),
    help="Override LLM provider",
)
@click.pass_context
def main(
    ctx: click.Context,
    config: Optional[Path],
    debug: bool,
    llm_provider: Optional[str],
):
    """
    InfraMaster - AI-driven toolbox for software infrastructure management.
    
    A modular toolbox for automating and enhancing software infrastructure
    tasks using artificial intelligence and modern development practices.
    """
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Load settings
    try:
        if config:
            # Load from specific config file
            settings = Settings(_env_file=str(config))
        else:
            settings = get_settings()
        
        # Override debug setting
        if debug:
            settings.debug = True
            settings.log_level = "DEBUG"
        
        # Override LLM provider
        if llm_provider:
            settings.llm_provider = LLMProvider(llm_provider)
        
        # Setup logging
        setup_logging(settings)
        
        # Store settings in context
        ctx.obj["settings"] = settings
        
    except Exception as e:
        console.print(f"[red]Error loading configuration: {e}[/red]")
        sys.exit(1)


@main.command()
@click.pass_context
def info(ctx: click.Context):
    """Show InfraMaster information and configuration."""
    settings: Settings = ctx.obj["settings"]
    
    # Create info table
    table = Table(title="InfraMaster Configuration")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="green")
    
    # Add configuration rows
    table.add_row("LLM Provider", settings.llm_provider.value)
    table.add_row("Debug Mode", str(settings.debug))
    table.add_row("Log Level", settings.log_level.value)
    
    if settings.llm_provider == LLMProvider.VERTEX_AI:
        table.add_row("Google Cloud Project", settings.google_cloud_project or "Not set")
        table.add_row("Vertex AI Location", settings.vertex_ai_location)
        table.add_row("Gemini Model", settings.gemini_model)
    elif settings.llm_provider == LLMProvider.OPENAI:
        table.add_row("OpenAI Model", settings.openai_model)
        table.add_row("API Key Set", "Yes" if settings.openai_api_key else "No")
    elif settings.llm_provider == LLMProvider.ANTHROPIC:
        table.add_row("Anthropic Model", settings.anthropic_model)
        table.add_row("API Key Set", "Yes" if settings.anthropic_api_key else "No")
    elif settings.llm_provider == LLMProvider.AZURE_OPENAI:
        table.add_row("Azure Endpoint", settings.azure_openai_endpoint or "Not set")
        table.add_row("Deployment Name", settings.azure_openai_deployment_name or "Not set")
        table.add_row("API Key Set", "Yes" if settings.azure_openai_api_key else "No")
    
    table.add_row("GitHub Token Set", "Yes" if settings.github_token else "No")
    
    console.print(table)


@main.command()
@click.pass_context
async def validate(ctx: click.Context):
    """Validate configuration and test connections."""
    settings: Settings = ctx.obj["settings"]
    
    console.print("[bold]Validating InfraMaster configuration...[/bold]")
    
    # Test LLM provider connection
    console.print(f"\n[cyan]Testing {settings.llm_provider.value} connection...[/cyan]")
    
    try:
        from inframaster.core.llm.factory import LLMFactory
        
        llm_provider = LLMFactory.create_provider(settings)
        is_connected = await llm_provider.validate_connection()
        
        if is_connected:
            console.print(f"[green]✓[/green] {settings.llm_provider.value} connection successful")
        else:
            console.print(f"[red]✗[/red] {settings.llm_provider.value} connection failed")
            
    except Exception as e:
        console.print(f"[red]✗[/red] {settings.llm_provider.value} connection error: {e}")
    
    # Test GitHub connection
    if settings.github_token:
        console.print(f"\n[cyan]Testing GitHub connection...[/cyan]")
        
        try:
            from inframaster.core.github.client import GitHubClient
            
            async with GitHubClient(settings.github_token) as github_client:
                # Simple test - try to access the authenticated user
                response = await github_client.http_client.get("/user")
                if response.status_code == 200:
                    user_data = response.json()
                    console.print(f"[green]✓[/green] GitHub connection successful (user: {user_data.get('login', 'unknown')})")
                else:
                    console.print(f"[red]✗[/red] GitHub connection failed: HTTP {response.status_code}")
                    
        except Exception as e:
            console.print(f"[red]✗[/red] GitHub connection error: {e}")
    else:
        console.print(f"\n[yellow]⚠[/yellow] GitHub token not configured")
    
    console.print("\n[bold]Validation complete![/bold]")


@main.command()
@click.option(
    "--output",
    type=click.Choice(["table", "json", "yaml"]),
    default="table",
    help="Output format",
)
@click.pass_context
def list_providers(ctx: click.Context, output: str):
    """List available LLM providers."""
    from inframaster.core.llm.factory import LLMFactory
    
    providers = LLMFactory.list_available_providers()
    
    if output == "table":
        table = Table(title="Available LLM Providers")
        table.add_column("Provider", style="cyan")
        table.add_column("Status", style="green")
        
        for provider in LLMProvider:
            status = "Available" if provider in providers else "Not Available"
            style = "green" if provider in providers else "red"
            table.add_row(provider.value, f"[{style}]{status}[/{style}]")
        
        console.print(table)
    
    elif output == "json":
        import json
        data = {
            "providers": [
                {
                    "name": provider.value,
                    "available": provider in providers
                }
                for provider in LLMProvider
            ]
        }
        console.print(json.dumps(data, indent=2))
    
    elif output == "yaml":
        import yaml
        data = {
            "providers": [
                {
                    "name": provider.value,
                    "available": provider in providers
                }
                for provider in LLMProvider
            ]
        }
        console.print(yaml.dump(data, default_flow_style=False))


# Add subcommands
main.add_command(code_review_cli, name="code-review")


def cli_main():
    """Entry point for the CLI."""
    # Handle async commands
    def run_async_command(func):
        def wrapper(*args, **kwargs):
            return asyncio.run(func(*args, **kwargs))
        return wrapper
    
    # Wrap async commands
    validate.callback = run_async_command(validate.callback)
    
    main()


if __name__ == "__main__":
    cli_main()
