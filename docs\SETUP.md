# InfraMaster Setup Guide

This guide will help you set up InfraMaster for AI-powered code review and other infrastructure management tasks.

## Prerequisites

- Python 3.9 or higher
- Git
- GitHub account with personal access token
- At least one LLM provider account (Google Cloud, OpenAI, Anthropic, or Azure OpenAI)

## Installation

### Option 1: Install from Source (Recommended for Development)

```bash
# Clone the repository
git clone https://github.com/inframaster/inframaster.git
cd inframaster

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install in development mode
pip install -e .

# Install development dependencies (optional)
pip install -e ".[dev]"
```

### Option 2: Install from PyPI (Future)

```bash
pip install inframaster
```

## Configuration

### 1. Environment Variables

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# General Settings
DEBUG=false
LOG_LEVEL=INFO

# LLM Provider (choose one)
LLM_PROVIDER=vertex_ai  # or openai, anthropic, azure_openai

# GitHub Configuration
GITHUB_TOKEN=your_github_token_here

# Provider-specific settings (configure based on your chosen provider)
```

### 2. GitHub Setup

1. **Create a Personal Access Token:**
   - Go to GitHub Settings → Developer settings → Personal access tokens
   - Generate a new token with these scopes:
     - `repo` (for private repositories)
     - `public_repo` (for public repositories)
     - `pull_requests:write` (to post review comments)

2. **Add token to environment:**
   ```bash
   export GITHUB_TOKEN=your_token_here
   ```

### 3. LLM Provider Setup

Choose one of the following providers:

#### Option A: Google Cloud Vertex AI (Recommended)

1. **Set up Google Cloud Project:**
   ```bash
   # Install Google Cloud CLI
   curl https://sdk.cloud.google.com | bash
   exec -l $SHELL
   
   # Login and set project
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   ```

2. **Enable Vertex AI API:**
   ```bash
   gcloud services enable aiplatform.googleapis.com
   ```

3. **Create Service Account:**
   ```bash
   gcloud iam service-accounts create inframaster \
     --description="InfraMaster service account" \
     --display-name="InfraMaster"
   
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:inframaster@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/aiplatform.user"
   
   gcloud iam service-accounts keys create credentials.json \
     --iam-account=inframaster@YOUR_PROJECT_ID.iam.gserviceaccount.com
   ```

4. **Configure environment:**
   ```env
   LLM_PROVIDER=vertex_ai
   GOOGLE_CLOUD_PROJECT=YOUR_PROJECT_ID
   GOOGLE_APPLICATION_CREDENTIALS=./credentials.json
   VERTEX_AI_LOCATION=us-central1
   GEMINI_MODEL=gemini-1.5-pro
   ```

#### Option B: OpenAI

1. **Get API Key:**
   - Sign up at https://platform.openai.com/
   - Create an API key in the API section

2. **Configure environment:**
   ```env
   LLM_PROVIDER=openai
   OPENAI_API_KEY=your_openai_api_key
   OPENAI_MODEL=gpt-4-turbo-preview
   ```

#### Option C: Anthropic

1. **Get API Key:**
   - Sign up at https://console.anthropic.com/
   - Create an API key

2. **Configure environment:**
   ```env
   LLM_PROVIDER=anthropic
   ANTHROPIC_API_KEY=your_anthropic_api_key
   ANTHROPIC_MODEL=claude-3-sonnet-********
   ```

#### Option D: Azure OpenAI

1. **Set up Azure OpenAI Resource:**
   - Create an Azure OpenAI resource in Azure Portal
   - Deploy a GPT-4 model

2. **Configure environment:**
   ```env
   LLM_PROVIDER=azure_openai
   AZURE_OPENAI_API_KEY=your_azure_api_key
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
   AZURE_OPENAI_API_VERSION=2024-02-15-preview
   AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4
   ```

## Verification

### 1. Test Installation

```bash
# Check if InfraMaster is installed correctly
inframaster --help

# Show configuration
inframaster info
```

### 2. Validate Configuration

```bash
# Test all connections
inframaster validate
```

You should see output like:
```
✓ vertex_ai connection successful
✓ GitHub connection successful (user: your_username)
```

### 3. Test Code Review

```bash
# Run a dry-run code review on a public PR
inframaster code-review review https://github.com/octocat/Hello-World/pull/1 --dry-run
```

## Development Setup

If you're planning to contribute to InfraMaster:

### 1. Install Development Dependencies

```bash
pip install -e ".[dev]"
```

### 2. Set up Pre-commit Hooks

```bash
pre-commit install
```

### 3. Run Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=inframaster --cov-report=html
```

### 4. Code Quality Checks

```bash
# Format code
black .
isort .

# Lint code
flake8 .

# Type checking
mypy .
```

## Troubleshooting

### Common Issues

1. **Import Error: No module named 'inframaster'**
   ```bash
   # Make sure you're in the right virtual environment
   which python
   pip list | grep inframaster
   
   # Reinstall if necessary
   pip install -e .
   ```

2. **Authentication Errors**
   ```bash
   # Check GitHub token
   curl -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/user
   
   # Check Google Cloud authentication
   gcloud auth list
   ```

3. **LLM Provider Errors**
   ```bash
   # Test specific provider
   inframaster validate
   
   # Check API quotas and billing
   ```

4. **Permission Errors**
   ```bash
   # Make sure GitHub token has correct scopes
   # Make sure service account has correct roles
   ```

### Getting Help

- Check the [examples](../examples/) directory for usage examples
- Review the [CLI documentation](../examples/cli_examples.md)
- Open an issue on GitHub for bugs or feature requests

## Next Steps

Once you have InfraMaster set up:

1. **Try the basic examples:**
   ```bash
   python examples/basic_usage.py
   ```

2. **Review the CLI examples:**
   ```bash
   cat examples/cli_examples.md
   ```

3. **Set up CI/CD integration** (see CLI examples for GitHub Actions/GitLab CI)

4. **Explore advanced configuration options** in the code review module

## Security Considerations

- Store API keys securely (use environment variables, not hardcoded values)
- Use least-privilege access for service accounts
- Regularly rotate API keys
- Monitor API usage and costs
- Be careful with sensitive repositories (consider using private LLM deployments)

## Performance Tips

- Use appropriate file limits for large PRs (`--max-files`)
- Consider using faster models for initial reviews
- Implement caching for repeated reviews
- Monitor API rate limits and costs
