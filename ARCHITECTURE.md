# InfraMaster Architecture

This document describes the architecture and design principles of InfraMaster, an AI-driven toolbox for software infrastructure management.

## Overview

InfraMaster is designed as a modular, extensible platform that leverages artificial intelligence to automate and enhance software infrastructure tasks. The first module implements AI-powered code review capabilities.

## Design Principles

1. **Modularity**: Each feature is implemented as a separate module with clear interfaces
2. **Extensibility**: Easy to add new LLM providers, GitHub integrations, and infrastructure tools
3. **Flexibility**: Configurable behavior through environment variables and configuration files
4. **Reliability**: Comprehensive error handling, logging, and testing
5. **Performance**: Efficient processing with rate limiting and caching considerations

## Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                    User Interfaces                         │
├─────────────────────┬───────────────────┬───────────────────┤
│        CLI          │    Python API     │   REST API        │
│   (click-based)     │   (direct import) │   (FastAPI)       │
└─────────────────────┴───────────────────┴───────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                      Modules                               │
├─────────────────────┬───────────────────┬───────────────────┤
│   Code Review       │   Infrastructure  │    Security       │
│   (implemented)     │   Monitoring      │    Scanning       │
│                     │   (future)        │    (future)       │
└─────────────────────┴───────────────────┴───────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Core Components                         │
├─────────────────────┬───────────────────┬───────────────────┤
│   LLM Abstraction   │   GitHub          │   Configuration   │
│   Layer             │   Integration     │   Management      │
└─────────────────────┴───────────────────┴───────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                 External Services                          │
├─────────────────────┬───────────────────┬───────────────────┤
│   LLM Providers     │   GitHub API      │   MCP Servers     │
│   (Vertex AI,       │   (REST API)      │   (GitHub MCP)    │
│   OpenAI, etc.)     │                   │                   │
└─────────────────────┴───────────────────┴───────────────────┘
```

## Core Components

### 1. Configuration Management (`inframaster.core.config`)

- **Purpose**: Centralized configuration handling
- **Key Classes**: `Settings`, `LLMProvider`, `LogLevel`
- **Features**:
  - Environment variable support
  - Configuration validation
  - Provider-specific settings
  - Type safety with Pydantic

### 2. LLM Abstraction Layer (`inframaster.core.llm`)

- **Purpose**: Unified interface for multiple LLM providers
- **Key Classes**: `BaseLLMProvider`, `LLMFactory`, `LLMResponse`
- **Providers**:
  - Vertex AI (Google Cloud)
  - OpenAI
  - Anthropic
  - Azure OpenAI
- **Features**:
  - Provider abstraction
  - Async support
  - Streaming responses
  - Connection validation

### 3. GitHub Integration (`inframaster.core.github`)

- **Purpose**: GitHub API integration and MCP support
- **Key Classes**: `GitHubClient`, `GitHubMCPClient`, `PullRequest`
- **Features**:
  - PR retrieval and analysis
  - Comment posting
  - Rate limiting
  - Error handling
  - MCP server integration

### 4. Logging System (`inframaster.core.logging`)

- **Purpose**: Structured logging with rich formatting
- **Features**:
  - Structured logging with structlog
  - Rich console output
  - Configurable log levels
  - Context preservation

## Code Review Module

### Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                   CodeReviewAgent                          │
│              (Main orchestrator)                           │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ CodeAnalyzer│ │ CodeReviewer│ │ GitHubClient│
│ (Static     │ │ (AI-powered │ │ (GitHub API │
│  Analysis)  │ │  Review)    │ │  Integration│
└─────────────┘ └─────────────┘ └─────────────┘
```

### Components

1. **CodeReviewAgent** (`inframaster.modules.code_review.agent`)
   - Main orchestrator for the review process
   - Coordinates between analyzer, reviewer, and GitHub client
   - Handles workflow and error management

2. **CodeAnalyzer** (`inframaster.modules.code_review.analyzer`)
   - Static code analysis
   - Language detection
   - Pattern-based issue detection
   - Complexity calculation

3. **CodeReviewer** (`inframaster.modules.code_review.reviewer`)
   - AI-powered code review
   - LLM integration for intelligent analysis
   - Review comment generation
   - Context-aware suggestions

4. **Data Models** (`inframaster.modules.code_review.models`)
   - Type-safe data structures
   - Review configuration
   - Issue classification
   - Result aggregation

### Workflow

1. **Input Processing**
   - Parse GitHub PR URL
   - Retrieve PR metadata and file changes
   - Build review context

2. **Static Analysis**
   - Analyze each changed file
   - Detect language-specific issues
   - Calculate complexity metrics
   - Generate initial issue list

3. **AI Review**
   - Enhance analysis with LLM insights
   - Generate intelligent suggestions
   - Classify issues by severity and category
   - Create comprehensive review

4. **Output Generation**
   - Format review comments
   - Calculate overall scores
   - Generate recommendations
   - Post to GitHub (if enabled)

## Extension Points

### Adding New LLM Providers

1. Implement `BaseLLMProvider` interface
2. Register with `LLMFactory`
3. Add configuration options to `Settings`
4. Update documentation

### Adding New Modules

1. Create module directory under `inframaster.modules`
2. Implement module-specific logic
3. Add CLI commands
4. Update main imports and documentation

### Adding New GitHub Integrations

1. Extend `GitHubClient` or create new client
2. Add new data models if needed
3. Implement MCP server extensions
4. Add configuration options

## Security Considerations

1. **API Key Management**
   - Environment variable storage
   - No hardcoded secrets
   - Secure credential handling

2. **GitHub Permissions**
   - Minimal required scopes
   - Token validation
   - Rate limiting compliance

3. **LLM Data Privacy**
   - No sensitive data in prompts
   - Provider-specific privacy controls
   - Audit logging

4. **Input Validation**
   - URL validation
   - Configuration validation
   - Error boundary handling

## Performance Considerations

1. **Rate Limiting**
   - GitHub API rate limits
   - LLM provider limits
   - Exponential backoff

2. **Caching**
   - Configuration caching
   - Response caching (future)
   - Connection pooling

3. **Async Processing**
   - Non-blocking I/O
   - Concurrent file analysis
   - Streaming responses

## Testing Strategy

1. **Unit Tests**
   - Individual component testing
   - Mock external dependencies
   - Configuration validation

2. **Integration Tests**
   - End-to-end workflow testing
   - Provider integration testing
   - Error handling validation

3. **Performance Tests**
   - Rate limiting behavior
   - Large PR handling
   - Memory usage optimization

## Future Enhancements

1. **Additional Modules**
   - Infrastructure monitoring
   - Security scanning
   - Performance analysis
   - Documentation generation

2. **Enhanced AI Features**
   - Multi-model consensus
   - Learning from feedback
   - Custom rule training

3. **Improved Integrations**
   - GitLab support
   - Bitbucket support
   - Slack/Teams notifications

4. **Advanced Analytics**
   - Review quality metrics
   - Team productivity insights
   - Code quality trends
