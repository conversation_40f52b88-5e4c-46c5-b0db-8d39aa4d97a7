"""
Main <PERSON><PERSON><PERSON><PERSON> agent for code review orchestration.

Coordinates the entire code review process from PR retrieval
to generating and posting review comments.
"""

import time
from typing import List, Optional

from inframaster.core.config import Settings
from inframaster.core.github.client import GitHubClient
from inframaster.core.github.models import Review, ReviewComment
from inframaster.core.llm.factory import LLMFactory
from inframaster.core.logging import get_logger
from inframaster.modules.code_review.analyzer import CodeAnalyzer
from inframaster.modules.code_review.models import (
    ReviewConfig,
    ReviewContext,
    ReviewResult,
    ReviewSeverity,
)
from inframaster.modules.code_review.reviewer import CodeReviewer

logger = get_logger(__name__)


class CodeReviewAgent:
    """
    Main agent for AI-powered code review.
    
    Orchestrates the entire code review process using LangChain
    and multiple AI providers.
    """
    
    def __init__(self, settings: Settings, config: Optional[ReviewConfig] = None):
        """
        Initialize the code review agent.
        
        Args:
            settings: Application settings
            config: Review configuration (optional)
        """
        self.settings = settings
        self.config = config or ReviewConfig()
        
        # Initialize components
        self.llm_provider = LLMFactory.create_provider(settings)
        self.analyzer = CodeAnalyzer()
        self.reviewer = CodeReviewer(self.llm_provider, self.config)
        
        logger.info("Initialized CodeReviewAgent")
    
    async def review_pr(
        self,
        pr_url: str,
        dry_run: bool = False,
        post_comments: Optional[bool] = None,
    ) -> ReviewResult:
        """
        Review a GitHub pull request.
        
        Args:
            pr_url: GitHub PR URL
            dry_run: If True, don't post comments to GitHub
            post_comments: Override config setting for posting comments
            
        Returns:
            Complete review result
            
        Raises:
            GitHubError: If GitHub API operations fail
            ValueError: If PR URL is invalid
        """
        start_time = time.time()
        
        logger.info(f"Starting code review for PR: {pr_url}")
        
        # Parse PR URL
        async with GitHubClient(self.settings.github_token) as github_client:
            owner, repo, pr_number = github_client.parse_pr_url(pr_url)
            
            # Get PR information
            pull_request = await github_client.get_pull_request(owner, repo, pr_number)
            
            logger.info(
                f"Retrieved PR #{pr_number}: {pull_request.title} "
                f"({pull_request.changed_files} files, "
                f"+{pull_request.additions}/-{pull_request.deletions})"
            )
            
            # Check if PR is too large
            if pull_request.changed_files > self.config.max_files_to_review:
                logger.warning(
                    f"PR has {pull_request.changed_files} files, "
                    f"exceeding limit of {self.config.max_files_to_review}"
                )
                # Could implement chunked review here
            
            # Build review context
            context = ReviewContext(
                repository_name=pull_request.base_repo.full_name,
                default_branch=pull_request.base_repo.default_branch,
                programming_languages=list(set(
                    self.analyzer._detect_language(file.filename) or "unknown"
                    for file in pull_request.files
                )),
                pr_title=pull_request.title,
                pr_description=pull_request.body,
                pr_labels=pull_request.labels,
                is_draft=pull_request.draft,
                author_login=pull_request.user.login,
            )
            
            # Analyze files
            file_analyses = []
            for file_change in pull_request.files:
                if len(file_analyses) >= self.config.max_files_to_review:
                    break
                    
                analysis = self.analyzer.analyze_file_change(file_change)
                file_analyses.append(analysis)
            
            logger.info(f"Analyzed {len(file_analyses)} files")
            
            # Generate AI review
            review_result = await self.reviewer.review_files(file_analyses, context)
            
            # Set PR metadata
            review_result.pr_number = pr_number
            review_result.pr_url = pr_url
            review_result.processing_time = time.time() - start_time
            
            # Post comments if enabled
            should_post = post_comments if post_comments is not None else self.config.post_comments
            if should_post and not dry_run:
                await self._post_review_comments(
                    github_client, owner, repo, pr_number, review_result
                )
            elif dry_run:
                logger.info("Dry run mode: skipping comment posting")
            
            logger.info(
                f"Code review completed in {review_result.processing_time:.2f}s: "
                f"{review_result.total_issues} issues found, "
                f"recommendation: {review_result.recommendation}"
            )
            
            return review_result
    
    async def _post_review_comments(
        self,
        github_client: GitHubClient,
        owner: str,
        repo: str,
        pr_number: int,
        review_result: ReviewResult,
    ) -> None:
        """Post review comments to GitHub."""
        
        logger.info("Posting review comments to GitHub")
        
        # Collect all comments to post
        review_comments = []
        
        for file_analysis in review_result.files_analyzed:
            for issue in file_analysis.issues:
                # Only post significant issues
                if issue.severity in [ReviewSeverity.CRITICAL, ReviewSeverity.MAJOR]:
                    comment = ReviewComment(
                        body=self._format_issue_comment(issue),
                        path=file_analysis.file_path,
                        line=issue.line_number,
                        side="RIGHT",
                    )
                    review_comments.append(comment)
        
        # Create review with comments
        if review_comments or review_result.summary:
            review_body = self._format_review_summary(review_result)
            
            review = Review(
                body=review_body,
                state=self._get_review_state(review_result),
                comments=review_comments,
            )
            
            try:
                posted_review = await github_client.create_review(
                    owner, repo, pr_number, review
                )
                logger.info(f"Posted review with {len(review_comments)} comments")
                
            except Exception as e:
                logger.error(f"Failed to post review: {e}")
                
                # Fallback: post individual comments
                for comment in review_comments:
                    try:
                        await github_client.post_review_comment(
                            owner, repo, pr_number, comment
                        )
                    except Exception as comment_error:
                        logger.error(f"Failed to post comment: {comment_error}")
    
    def _format_issue_comment(self, issue) -> str:
        """Format an issue as a GitHub comment."""
        severity_emoji = {
            ReviewSeverity.CRITICAL: "🚨",
            ReviewSeverity.MAJOR: "⚠️",
            ReviewSeverity.MINOR: "💡",
            ReviewSeverity.INFO: "ℹ️",
        }
        
        category_emoji = {
            "bugs": "🐛",
            "security": "🔒",
            "performance": "⚡",
            "maintainability": "🔧",
            "style": "🎨",
            "documentation": "📝",
            "testing": "🧪",
            "architecture": "🏗️",
            "best_practices": "✨",
        }
        
        emoji = severity_emoji.get(issue.severity, "")
        cat_emoji = category_emoji.get(issue.category.value, "")
        
        comment = f"{emoji} **{issue.title}** {cat_emoji}\n\n"
        comment += f"{issue.description}\n\n"
        
        if issue.suggestion:
            comment += f"**Suggestion:** {issue.suggestion}\n\n"
        
        comment += f"*Severity: {issue.severity.value.title()} | "
        comment += f"Category: {issue.category.value.replace('_', ' ').title()} | "
        comment += f"Confidence: {issue.confidence:.0%}*"
        
        return comment
    
    def _format_review_summary(self, review_result: ReviewResult) -> str:
        """Format the overall review summary."""
        summary = f"## 🤖 AI Code Review Summary\n\n"
        
        if review_result.summary:
            summary += f"{review_result.summary}\n\n"
        
        # Statistics
        summary += f"**Files Analyzed:** {len(review_result.files_analyzed)}\n"
        summary += f"**Total Issues:** {review_result.total_issues}\n"
        
        if review_result.overall_score is not None:
            summary += f"**Overall Score:** {review_result.overall_score:.1f}/10\n"
        
        # Issue breakdown
        if review_result.issues_by_severity:
            summary += "\n**Issues by Severity:**\n"
            for severity, count in review_result.issues_by_severity.items():
                if count > 0:
                    summary += f"- {severity.value.title()}: {count}\n"
        
        # Recommendation
        rec_emoji = {
            "approve": "✅",
            "request_changes": "❌", 
            "comment": "💬",
        }
        
        if review_result.recommendation:
            emoji = rec_emoji.get(review_result.recommendation, "")
            rec_text = review_result.recommendation.replace("_", " ").title()
            summary += f"\n**Recommendation:** {emoji} {rec_text}\n"
        
        summary += f"\n*Review completed by {review_result.reviewer_provider} "
        summary += f"({review_result.reviewer_model}) in "
        summary += f"{review_result.processing_time:.1f}s*"
        
        return summary
    
    def _get_review_state(self, review_result: ReviewResult) -> str:
        """Get the GitHub review state based on the result."""
        if review_result.recommendation == "approve":
            return "APPROVED"
        elif review_result.recommendation == "request_changes":
            return "CHANGES_REQUESTED"
        else:
            return "COMMENTED"
