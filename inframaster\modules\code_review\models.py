"""
Data models for the code review module.

Defines the data structures used throughout the code review process.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any

from pydantic import BaseModel, Field


class ReviewSeverity(str, Enum):
    """Severity levels for review comments."""
    INFO = "info"
    MINOR = "minor"
    MAJOR = "major"
    CRITICAL = "critical"


class ReviewCategory(str, Enum):
    """Categories for review comments."""
    BUGS = "bugs"
    SECURITY = "security"
    PERFORMANCE = "performance"
    MAINTAINABILITY = "maintainability"
    STYLE = "style"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    ARCHITECTURE = "architecture"
    BEST_PRACTICES = "best_practices"


class CodeIssue(BaseModel):
    """Represents a code issue found during review."""
    file_path: str
    line_number: Optional[int] = None
    line_end: Optional[int] = None
    severity: ReviewSeverity
    category: ReviewCategory
    title: str
    description: str
    suggestion: Optional[str] = None
    code_snippet: Optional[str] = None
    confidence: float = Field(ge=0.0, le=1.0, default=0.8)
    
    # Additional context
    rule_id: Optional[str] = None
    references: List[str] = []
    tags: List[str] = []


class FileAnalysis(BaseModel):
    """Analysis results for a single file."""
    file_path: str
    language: Optional[str] = None
    lines_added: int = 0
    lines_removed: int = 0
    complexity_score: Optional[float] = None
    
    # Issues found in this file
    issues: List[CodeIssue] = []
    
    # File-level metrics
    metrics: Dict[str, Any] = {}
    
    # Summary
    summary: Optional[str] = None


class ReviewResult(BaseModel):
    """Complete review result for a pull request."""
    pr_url: str
    pr_number: int
    repository: str
    
    # Review metadata
    reviewed_at: datetime = Field(default_factory=datetime.now)
    reviewer_model: str
    reviewer_provider: str
    
    # Analysis results
    files_analyzed: List[FileAnalysis] = []
    total_issues: int = 0
    issues_by_severity: Dict[ReviewSeverity, int] = {}
    issues_by_category: Dict[ReviewCategory, int] = {}
    
    # Overall assessment
    overall_score: Optional[float] = Field(ge=0.0, le=10.0, default=None)
    recommendation: Optional[str] = None  # "approve", "request_changes", "comment"
    summary: Optional[str] = None
    
    # Processing metadata
    processing_time: Optional[float] = None
    tokens_used: Optional[int] = None
    
    @property
    def critical_issues(self) -> List[CodeIssue]:
        """Get all critical issues across all files."""
        critical = []
        for file_analysis in self.files_analyzed:
            critical.extend([
                issue for issue in file_analysis.issues 
                if issue.severity == ReviewSeverity.CRITICAL
            ])
        return critical
    
    @property
    def has_blocking_issues(self) -> bool:
        """Check if there are any blocking (critical) issues."""
        return len(self.critical_issues) > 0


class ReviewConfig(BaseModel):
    """Configuration for code review process."""
    
    # Analysis settings
    max_files_to_review: int = 50
    max_file_size_kb: int = 100
    skip_generated_files: bool = True
    skip_test_files: bool = False
    
    # Review focus areas
    focus_on_security: bool = True
    focus_on_performance: bool = True
    focus_on_maintainability: bool = True
    focus_on_style: bool = False
    
    # Severity thresholds
    min_confidence_threshold: float = 0.6
    auto_approve_threshold: float = 8.5
    request_changes_threshold: float = 6.0
    
    # Comment settings
    post_comments: bool = True
    post_summary_comment: bool = True
    group_similar_issues: bool = True
    
    # Language-specific settings
    language_configs: Dict[str, Dict[str, Any]] = {}
    
    # Custom rules
    custom_rules: List[str] = []
    ignore_patterns: List[str] = []


class ReviewContext(BaseModel):
    """Context information for the review process."""
    
    # Repository context
    repository_name: str
    default_branch: str
    programming_languages: List[str] = []
    
    # PR context
    pr_title: str
    pr_description: Optional[str] = None
    pr_labels: List[str] = []
    is_draft: bool = False
    
    # Author context
    author_login: str
    is_first_time_contributor: bool = False
    
    # Historical context
    similar_prs: List[Dict[str, Any]] = []
    recent_issues: List[Dict[str, Any]] = []
    
    # Team context
    team_guidelines: Optional[str] = None
    coding_standards: Optional[str] = None
