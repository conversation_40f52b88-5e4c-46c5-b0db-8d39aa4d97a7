"""
AI-powered code reviewer using LLM providers.

Generates intelligent review comments and suggestions using
large language models with proper context and formatting.
"""

import json
from typing import List, Optional

from inframaster.core.llm.base import Base<PERSON><PERSON>rovider, LLMMessage
from inframaster.core.logging import get_logger
from inframaster.modules.code_review.models import (
    CodeIssue,
    FileAnalysis,
    ReviewCategory,
    ReviewConfig,
    ReviewContext,
    ReviewResult,
    ReviewSeverity,
)

logger = get_logger(__name__)


class CodeReviewer:
    """AI-powered code reviewer using LLM providers."""
    
    def __init__(self, llm_provider: BaseLLMProvider, config: ReviewConfig):
        """
        Initialize the code reviewer.
        
        Args:
            llm_provider: LLM provider for generating reviews
            config: Review configuration
        """
        self.llm_provider = llm_provider
        self.config = config
        logger.info(f"Initialized code reviewer with {llm_provider.provider_name}")
    
    async def review_files(
        self,
        file_analyses: List[FileAnalysis],
        context: ReviewContext,
    ) -> ReviewResult:
        """
        Review analyzed files and generate comprehensive feedback.
        
        Args:
            file_analyses: List of file analyses to review
            context: Review context information
            
        Returns:
            Complete review result with recommendations
        """
        logger.info(f"Starting AI review of {len(file_analyses)} files")
        
        # Initialize review result
        result = ReviewResult(
            pr_url=f"https://github.com/{context.repository_name}/pull/{context.pr_title}",
            pr_number=0,  # Will be set by caller
            repository=context.repository_name,
            reviewer_model=self.llm_provider.model_name,
            reviewer_provider=self.llm_provider.provider_name,
        )
        
        # Process each file
        for file_analysis in file_analyses:
            if self._should_skip_file(file_analysis):
                continue
                
            # Enhance analysis with AI insights
            enhanced_analysis = await self._enhance_file_analysis(file_analysis, context)
            result.files_analyzed.append(enhanced_analysis)
        
        # Generate overall assessment
        await self._generate_overall_assessment(result, context)
        
        # Calculate statistics
        self._calculate_statistics(result)
        
        logger.info(f"Completed AI review: {result.total_issues} issues found")
        return result
    
    def _should_skip_file(self, file_analysis: FileAnalysis) -> bool:
        """Check if a file should be skipped during review."""
        # Skip if no language detected
        if not file_analysis.language:
            return True
        
        # Skip test files if configured
        if self.config.skip_test_files and self._is_test_file(file_analysis.file_path):
            return True
        
        # Skip large files
        if file_analysis.lines_added > self.config.max_file_size_kb * 10:  # Rough estimate
            return True
        
        return False
    
    def _is_test_file(self, file_path: str) -> bool:
        """Check if a file is a test file."""
        test_patterns = [
            "test_", "_test.", "tests/", "/test/", ".test.", ".spec.",
            "spec_", "_spec.", "specs/", "/spec/", "__tests__/",
        ]
        return any(pattern in file_path.lower() for pattern in test_patterns)
    
    async def _enhance_file_analysis(
        self,
        file_analysis: FileAnalysis,
        context: ReviewContext,
    ) -> FileAnalysis:
        """Enhance file analysis with AI-generated insights."""
        
        # Prepare context for the LLM
        messages = self._prepare_file_review_messages(file_analysis, context)
        
        try:
            # Generate AI review
            response = await self.llm_provider.generate(
                messages=messages,
                temperature=0.3,  # Lower temperature for more consistent reviews
                max_tokens=2000,
            )
            
            # Parse AI response and enhance analysis
            ai_insights = self._parse_ai_response(response.content)
            
            # Add AI-generated issues
            for insight in ai_insights.get("issues", []):
                issue = self._create_issue_from_insight(insight, file_analysis.file_path)
                if issue and issue.confidence >= self.config.min_confidence_threshold:
                    file_analysis.issues.append(issue)
            
            # Update summary with AI insights
            if ai_insights.get("summary"):
                file_analysis.summary = ai_insights["summary"]
            
            # Update complexity score if provided
            if ai_insights.get("complexity_score"):
                file_analysis.complexity_score = ai_insights["complexity_score"]
            
        except Exception as e:
            logger.error(f"Error enhancing file analysis for {file_analysis.file_path}: {e}")
        
        return file_analysis
    
    def _prepare_file_review_messages(
        self,
        file_analysis: FileAnalysis,
        context: ReviewContext,
    ) -> List[LLMMessage]:
        """Prepare messages for file-level review."""
        
        system_prompt = self._get_system_prompt(context)
        
        # Prepare file information
        file_info = {
            "file_path": file_analysis.file_path,
            "language": file_analysis.language,
            "lines_added": file_analysis.lines_added,
            "lines_removed": file_analysis.lines_removed,
            "existing_issues": [
                {
                    "line": issue.line_number,
                    "severity": issue.severity.value,
                    "category": issue.category.value,
                    "title": issue.title,
                    "description": issue.description,
                }
                for issue in file_analysis.issues
            ],
        }
        
        user_prompt = f"""
Please review this file change and provide additional insights:

File Information:
{json.dumps(file_info, indent=2)}

Context:
- Repository: {context.repository_name}
- PR Title: {context.pr_title}
- Author: {context.author_login}
- Languages: {', '.join(context.programming_languages)}

Please provide your analysis in the following JSON format:
{{
    "issues": [
        {{
            "line_number": <line_number>,
            "severity": "critical|major|minor|info",
            "category": "bugs|security|performance|maintainability|style|documentation|testing|architecture|best_practices",
            "title": "<short_title>",
            "description": "<detailed_description>",
            "suggestion": "<actionable_suggestion>",
            "confidence": <0.0_to_1.0>
        }}
    ],
    "complexity_score": <0.0_to_10.0>,
    "summary": "<file_summary>",
    "recommendations": ["<recommendation1>", "<recommendation2>"]
}}

Focus on:
- Code quality and maintainability
- Potential bugs and edge cases
- Security vulnerabilities
- Performance implications
- Best practices for {file_analysis.language or 'the language'}
"""
        
        return [
            LLMMessage(role="system", content=system_prompt),
            LLMMessage(role="user", content=user_prompt),
        ]
    
    def _get_system_prompt(self, context: ReviewContext) -> str:
        """Get the system prompt for code review."""
        return f"""You are an expert code reviewer with deep knowledge of software engineering best practices, security, and performance optimization. You are reviewing a pull request for the {context.repository_name} repository.

Your role is to:
1. Identify potential bugs, security issues, and performance problems
2. Suggest improvements for code quality and maintainability
3. Ensure adherence to best practices and coding standards
4. Provide constructive, actionable feedback

Guidelines:
- Be thorough but focus on the most important issues
- Provide specific, actionable suggestions
- Consider the context of the entire codebase
- Be respectful and constructive in your feedback
- Assign appropriate severity levels based on impact
- Only flag issues you are confident about (confidence >= 0.6)

Review Focus Areas:
- Security: Look for potential vulnerabilities, hardcoded secrets, injection risks
- Performance: Identify inefficient algorithms, memory leaks, unnecessary computations
- Maintainability: Check for code clarity, proper naming, documentation
- Bugs: Find logical errors, edge cases, null pointer risks
- Best Practices: Ensure following language-specific conventions and patterns

Remember: Your goal is to help improve code quality while being helpful and educational."""
    
    def _parse_ai_response(self, response_content: str) -> dict:
        """Parse AI response into structured data."""
        try:
            # Try to extract JSON from the response
            json_start = response_content.find('{')
            json_end = response_content.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response_content[json_start:json_end]
                return json.loads(json_str)
            else:
                logger.warning("No JSON found in AI response")
                return {}
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            return {}
    
    def _create_issue_from_insight(self, insight: dict, file_path: str) -> Optional[CodeIssue]:
        """Create a CodeIssue from AI insight."""
        try:
            return CodeIssue(
                file_path=file_path,
                line_number=insight.get("line_number"),
                severity=ReviewSeverity(insight["severity"]),
                category=ReviewCategory(insight["category"]),
                title=insight["title"],
                description=insight["description"],
                suggestion=insight.get("suggestion"),
                confidence=insight.get("confidence", 0.8),
                rule_id="ai_generated",
                tags=["ai_review"],
            )
        except (KeyError, ValueError) as e:
            logger.warning(f"Failed to create issue from insight: {e}")
            return None
    
    async def _generate_overall_assessment(
        self,
        result: ReviewResult,
        context: ReviewContext,
    ) -> None:
        """Generate overall assessment and recommendation."""
        
        # Prepare summary data
        total_issues = sum(len(file_analysis.issues) for file_analysis in result.files_analyzed)
        critical_issues = sum(
            len([issue for issue in file_analysis.issues if issue.severity == ReviewSeverity.CRITICAL])
            for file_analysis in result.files_analyzed
        )
        major_issues = sum(
            len([issue for issue in file_analysis.issues if issue.severity == ReviewSeverity.MAJOR])
            for file_analysis in result.files_analyzed
        )
        
        # Calculate overall score (0-10)
        base_score = 8.0
        score_deduction = (critical_issues * 2.0) + (major_issues * 1.0) + (total_issues * 0.1)
        overall_score = max(0.0, base_score - score_deduction)
        
        result.overall_score = overall_score
        
        # Determine recommendation
        if overall_score >= self.config.auto_approve_threshold and critical_issues == 0:
            result.recommendation = "approve"
        elif overall_score < self.config.request_changes_threshold or critical_issues > 0:
            result.recommendation = "request_changes"
        else:
            result.recommendation = "comment"
        
        # Generate summary
        messages = [
            LLMMessage(
                role="system",
                content="You are a senior code reviewer providing a summary of a pull request review.",
            ),
            LLMMessage(
                role="user",
                content=f"""
Please provide a concise summary of this pull request review:

Repository: {context.repository_name}
PR Title: {context.pr_title}
Files Analyzed: {len(result.files_analyzed)}
Total Issues: {total_issues}
Critical Issues: {critical_issues}
Major Issues: {major_issues}
Overall Score: {overall_score:.1f}/10
Recommendation: {result.recommendation}

Provide a 2-3 sentence summary focusing on the key findings and overall assessment.
""",
            ),
        ]
        
        try:
            response = await self.llm_provider.generate(
                messages=messages,
                temperature=0.3,
                max_tokens=200,
            )
            result.summary = response.content.strip()
        except Exception as e:
            logger.error(f"Failed to generate overall summary: {e}")
            result.summary = f"Review completed with {total_issues} issues found. Overall score: {overall_score:.1f}/10."
    
    def _calculate_statistics(self, result: ReviewResult) -> None:
        """Calculate review statistics."""
        all_issues = []
        for file_analysis in result.files_analyzed:
            all_issues.extend(file_analysis.issues)
        
        result.total_issues = len(all_issues)
        
        # Count by severity
        for severity in ReviewSeverity:
            count = len([issue for issue in all_issues if issue.severity == severity])
            result.issues_by_severity[severity] = count
        
        # Count by category
        for category in ReviewCategory:
            count = len([issue for issue in all_issues if issue.category == category])
            result.issues_by_category[category] = count
