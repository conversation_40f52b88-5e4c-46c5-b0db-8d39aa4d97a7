"""
Factory for creating LLM provider instances.

Handles the instantiation of different LLM providers based on configuration.
"""

from typing import Dict, Type

from inframaster.core.config import LLMProvider, Settings
from inframaster.core.llm.base import BaseLLMProvider
from inframaster.core.logging import get_logger

logger = get_logger(__name__)


class LLMFactory:
    """Factory for creating LLM provider instances."""
    
    _providers: Dict[LLMProvider, Type[BaseLLMProvider]] = {}
    
    @classmethod
    def register_provider(
        cls, 
        provider_type: LLMProvider, 
        provider_class: Type[BaseLLMProvider]
    ) -> None:
        """
        Register a new LLM provider.
        
        Args:
            provider_type: The provider type enum
            provider_class: The provider class to instantiate
        """
        cls._providers[provider_type] = provider_class
        logger.info(f"Registered LLM provider: {provider_type.value}")
    
    @classmethod
    def create_provider(
        cls, 
        settings: Settings, 
        provider_override: LLMProvider = None
    ) -> BaseLLMProvider:
        """
        Create an LLM provider instance based on settings.
        
        Args:
            settings: Application settings
            provider_override: Override the configured provider
            
        Returns:
            Configured LLM provider instance
            
        Raises:
            ValueError: If the provider is not supported or not registered
        """
        provider_type = provider_override or settings.llm_provider
        
        if provider_type not in cls._providers:
            # Try to import and register providers if not already done
            cls._register_default_providers()
            
        if provider_type not in cls._providers:
            available = list(cls._providers.keys())
            raise ValueError(
                f"Unsupported LLM provider: {provider_type}. "
                f"Available providers: {available}"
            )
        
        provider_class = cls._providers[provider_type]
        
        # Create provider with appropriate configuration
        if provider_type == LLMProvider.VERTEX_AI:
            return provider_class(
                project_id=settings.google_cloud_project,
                location=settings.vertex_ai_location,
                model_name=settings.gemini_model,
                credentials_path=settings.google_application_credentials,
            )
        elif provider_type == LLMProvider.OPENAI:
            return provider_class(
                api_key=settings.openai_api_key,
                model_name=settings.openai_model,
                base_url=settings.openai_base_url,
            )
        elif provider_type == LLMProvider.ANTHROPIC:
            return provider_class(
                api_key=settings.anthropic_api_key,
                model_name=settings.anthropic_model,
            )
        elif provider_type == LLMProvider.AZURE_OPENAI:
            return provider_class(
                api_key=settings.azure_openai_api_key,
                endpoint=settings.azure_openai_endpoint,
                api_version=settings.azure_openai_api_version,
                deployment_name=settings.azure_openai_deployment_name,
            )
        else:
            # Fallback for custom providers
            return provider_class(**settings.dict())
    
    @classmethod
    def _register_default_providers(cls) -> None:
        """Register default LLM providers."""
        try:
            from inframaster.core.llm.providers.vertex_ai import VertexAIProvider
            cls.register_provider(LLMProvider.VERTEX_AI, VertexAIProvider)
        except ImportError:
            logger.warning("Vertex AI provider not available")
        
        try:
            from inframaster.core.llm.providers.openai import OpenAIProvider
            cls.register_provider(LLMProvider.OPENAI, OpenAIProvider)
        except ImportError:
            logger.warning("OpenAI provider not available")
        
        try:
            from inframaster.core.llm.providers.anthropic import AnthropicProvider
            cls.register_provider(LLMProvider.ANTHROPIC, AnthropicProvider)
        except ImportError:
            logger.warning("Anthropic provider not available")
        
        try:
            from inframaster.core.llm.providers.azure_openai import AzureOpenAIProvider
            cls.register_provider(LLMProvider.AZURE_OPENAI, AzureOpenAIProvider)
        except ImportError:
            logger.warning("Azure OpenAI provider not available")
    
    @classmethod
    def list_available_providers(cls) -> list[LLMProvider]:
        """
        List all available LLM providers.
        
        Returns:
            List of available provider types
        """
        if not cls._providers:
            cls._register_default_providers()
        return list(cls._providers.keys())
