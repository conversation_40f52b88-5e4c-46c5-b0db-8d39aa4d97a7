# InfraMaster CLI Examples

This document provides examples of using the InfraMaster command-line interface for AI-powered code review.

## Setup

First, install InfraMaster and configure your environment:

```bash
# Install InfraMaster
pip install -e .

# Copy and configure environment variables
cp .env.example .env
# Edit .env with your API keys and settings

# Verify installation
inframaster info
```

## Basic Code Review

Review a GitHub pull request:

```bash
# Basic review
inframaster code-review review https://github.com/owner/repo/pull/123

# Dry run (don't post comments)
inframaster code-review review https://github.com/owner/repo/pull/123 --dry-run

# Use specific LLM provider
inframaster code-review review https://github.com/owner/repo/pull/123 --llm-provider openai

# Focus on security issues
inframaster code-review review https://github.com/owner/repo/pull/123 --focus security
```

## Output Formats

Choose different output formats:

```bash
# Table format (default)
inframaster code-review review https://github.com/owner/repo/pull/123 --output table

# JSON format
inframaster code-review review https://github.com/owner/repo/pull/123 --output json

# Summary format
inframaster code-review review https://github.com/owner/repo/pull/123 --output summary
```

## Advanced Options

```bash
# Review with custom settings
inframaster code-review review https://github.com/owner/repo/pull/123 \
  --max-files 20 \
  --focus security \
  --no-post-comments \
  --output json

# Review large PR (increase file limit)
inframaster code-review review https://github.com/owner/repo/pull/123 \
  --max-files 100

# Force comment posting
inframaster code-review review https://github.com/owner/repo/pull/123 \
  --post-comments
```

## Configuration Management

```bash
# Show current configuration
inframaster code-review config --show

# Validate configuration and test connections
inframaster validate

# List available LLM providers
inframaster list-providers

# Show provider status in JSON
inframaster list-providers --output json
```

## Using Different LLM Providers

### Vertex AI (Google Cloud)

```bash
# Set up Vertex AI
export GOOGLE_CLOUD_PROJECT=your-project-id
export GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Review with Vertex AI
inframaster code-review review https://github.com/owner/repo/pull/123 \
  --llm-provider vertex_ai
```

### OpenAI

```bash
# Set up OpenAI
export OPENAI_API_KEY=your-api-key

# Review with OpenAI
inframaster code-review review https://github.com/owner/repo/pull/123 \
  --llm-provider openai
```

### Anthropic

```bash
# Set up Anthropic
export ANTHROPIC_API_KEY=your-api-key

# Review with Anthropic
inframaster code-review review https://github.com/owner/repo/pull/123 \
  --llm-provider anthropic
```

### Azure OpenAI

```bash
# Set up Azure OpenAI
export AZURE_OPENAI_API_KEY=your-api-key
export AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
export AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4

# Review with Azure OpenAI
inframaster code-review review https://github.com/owner/repo/pull/123 \
  --llm-provider azure_openai
```

## Batch Processing

Review multiple PRs using shell scripting:

```bash
#!/bin/bash
# review_batch.sh

PRS=(
  "https://github.com/owner/repo/pull/123"
  "https://github.com/owner/repo/pull/124"
  "https://github.com/owner/repo/pull/125"
)

for pr in "${PRS[@]}"; do
  echo "Reviewing $pr..."
  inframaster code-review review "$pr" --output summary
  echo "---"
done
```

## Integration with CI/CD

### GitHub Actions

```yaml
# .github/workflows/code-review.yml
name: AI Code Review

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  ai-review:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install InfraMaster
        run: |
          pip install inframaster
      
      - name: Run AI Code Review
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          LLM_PROVIDER: openai
        run: |
          inframaster code-review review ${{ github.event.pull_request.html_url }}
```

### GitLab CI

```yaml
# .gitlab-ci.yml
ai-code-review:
  stage: review
  image: python:3.11
  script:
    - pip install inframaster
    - inframaster code-review review $CI_MERGE_REQUEST_PROJECT_URL/-/merge_requests/$CI_MERGE_REQUEST_IID
  only:
    - merge_requests
  variables:
    GITHUB_TOKEN: $GITHUB_TOKEN
    OPENAI_API_KEY: $OPENAI_API_KEY
    LLM_PROVIDER: openai
```

## Troubleshooting

### Common Issues

1. **Authentication Error**
   ```bash
   # Check GitHub token
   inframaster validate
   
   # Test GitHub connection
   curl -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/user
   ```

2. **LLM Provider Error**
   ```bash
   # List available providers
   inframaster list-providers
   
   # Test specific provider
   inframaster validate
   ```

3. **Rate Limiting**
   ```bash
   # Check rate limits
   curl -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/rate_limit
   ```

### Debug Mode

Enable debug logging for troubleshooting:

```bash
# Enable debug mode
inframaster --debug code-review review https://github.com/owner/repo/pull/123

# Or set environment variable
export DEBUG=true
export LOG_LEVEL=DEBUG
inframaster code-review review https://github.com/owner/repo/pull/123
```

## Best Practices

1. **Use dry-run for testing**
   ```bash
   inframaster code-review review $PR_URL --dry-run
   ```

2. **Focus reviews on specific areas**
   ```bash
   inframaster code-review review $PR_URL --focus security
   ```

3. **Limit file count for large PRs**
   ```bash
   inframaster code-review review $PR_URL --max-files 30
   ```

4. **Use appropriate output format**
   ```bash
   # For human reading
   inframaster code-review review $PR_URL --output summary
   
   # For automation
   inframaster code-review review $PR_URL --output json
   ```

5. **Validate configuration before use**
   ```bash
   inframaster validate
   ```
