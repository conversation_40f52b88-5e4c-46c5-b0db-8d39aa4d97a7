# InfraMaster Configuration Example
# Copy this file to .env and fill in your values

# General Settings
DEBUG=false
LOG_LEVEL=INFO

# LLM Provider Configuration
# Options: vertex_ai, openai, anthropic, azure_openai
LLM_PROVIDER=vertex_ai

# Vertex AI / Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
VERTEX_AI_LOCATION=us-central1
GEMINI_MODEL=gemini-1.5-pro

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4-turbo-preview
# OPENAI_BASE_URL=https://api.openai.com/v1  # Optional custom base URL

# Anthropic Configuration
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet-********

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your-azure-openai-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4

# GitHub Configuration
GITHUB_TOKEN=your-github-personal-access-token
GITHUB_MCP_SERVER_URL=https://api.github.com

# Code Review Settings
CODE_REVIEW_MAX_FILES=50
CODE_REVIEW_MAX_DIFF_SIZE=10000
CODE_REVIEW_POST_COMMENTS=true

# Rate Limiting
GITHUB_RATE_LIMIT_REQUESTS=5000
LLM_RATE_LIMIT_REQUESTS=100
