"""
OpenAI LLM provider implementation.

Provides integration with OpenAI's GPT models.
"""

from typing import List, Optional

from langchain_openai import ChatOpenAI

from inframaster.core.llm.base import BaseLLMProvider, LLMMessage, LLMResponse
from inframaster.core.logging import get_logger

logger = get_logger(__name__)


class OpenAIProvider(BaseLLMProvider):
    """OpenAI LLM provider using GPT models."""
    
    def __init__(
        self,
        api_key: str,
        model_name: str = "gpt-4-turbo-preview",
        base_url: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize OpenAI provider.
        
        Args:
            api_key: OpenAI API key
            model_name: GPT model to use
            base_url: Custom API base URL
            **kwargs: Additional configuration
        """
        super().__init__(**kwargs)
        
        self.api_key = api_key
        self._model_name = model_name
        self.base_url = base_url
        
        # Initialize the LangChain OpenAI client
        client_kwargs = {
            "model": model_name,
            "openai_api_key": api_key,
            "temperature": 0.7,
            "max_tokens": 4096,
        }
        
        if base_url:
            client_kwargs["openai_api_base"] = base_url
        
        self.client = ChatOpenAI(**client_kwargs)
        
        logger.info(f"Initialized OpenAI provider with model: {model_name}")
    
    @property
    def provider_name(self) -> str:
        """Return the provider name."""
        return "openai"
    
    @property
    def model_name(self) -> str:
        """Return the model name."""
        return self._model_name
    
    async def generate(
        self,
        messages: List[LLMMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Generate a response using OpenAI.
        
        Args:
            messages: List of conversation messages
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Returns:
            LLMResponse with generated content
        """
        try:
            # Update client parameters
            self.client.temperature = temperature
            if max_tokens:
                self.client.max_tokens = max_tokens
            
            # Convert messages to LangChain format
            langchain_messages = self._convert_to_langchain_messages(messages)
            
            # Generate response
            response = await self.client.ainvoke(langchain_messages)
            
            return LLMResponse(
                content=response.content,
                model=self._model_name,
                provider=self.provider_name,
                usage=getattr(response, "usage_metadata", None),
                metadata={
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "base_url": self.base_url,
                }
            )
            
        except Exception as e:
            logger.error(f"Error generating response with OpenAI: {e}")
            raise
    
    async def generate_stream(
        self,
        messages: List[LLMMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ):
        """
        Generate a streaming response using OpenAI.
        
        Args:
            messages: List of conversation messages
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Yields:
            Partial LLMResponse objects
        """
        try:
            # Update client parameters
            self.client.temperature = temperature
            if max_tokens:
                self.client.max_tokens = max_tokens
            
            # Convert messages to LangChain format
            langchain_messages = self._convert_to_langchain_messages(messages)
            
            # Generate streaming response
            accumulated_content = ""
            async for chunk in self.client.astream(langchain_messages):
                if chunk.content:
                    accumulated_content += chunk.content
                    yield LLMResponse(
                        content=accumulated_content,
                        model=self._model_name,
                        provider=self.provider_name,
                        metadata={
                            "streaming": True,
                            "base_url": self.base_url,
                        }
                    )
                    
        except Exception as e:
            logger.error(f"Error generating streaming response with OpenAI: {e}")
            raise
    
    async def validate_connection(self) -> bool:
        """
        Validate connection to OpenAI.
        
        Returns:
            True if connection is successful
        """
        try:
            # Test with a simple message
            test_messages = [
                LLMMessage(role="user", content="Hello, can you respond with 'OK'?")
            ]
            response = await self.generate(test_messages, max_tokens=10)
            return bool(response.content)
            
        except Exception as e:
            logger.error(f"OpenAI connection validation failed: {e}")
            return False
    
    def _convert_to_langchain_messages(self, messages: List[LLMMessage]):
        """Convert LLMMessage objects to LangChain message format."""
        from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
        
        langchain_messages = []
        for msg in messages:
            if msg.role == "system":
                langchain_messages.append(SystemMessage(content=msg.content))
            elif msg.role == "user":
                langchain_messages.append(HumanMessage(content=msg.content))
            elif msg.role == "assistant":
                langchain_messages.append(AIMessage(content=msg.content))
            else:
                # Default to human message for unknown roles
                langchain_messages.append(HumanMessage(content=msg.content))
        
        return langchain_messages
