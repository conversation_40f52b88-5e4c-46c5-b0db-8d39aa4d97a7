"""
Configuration management for InfraMaster.

Handles environment variables, settings validation, and configuration
for different components of the system.
"""

import os
from enum import Enum
from pathlib import Path
from typing import Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    VERTEX_AI = "vertex_ai"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    AZURE_OPENAI = "azure_openai"


class LogLevel(str, Enum):
    """Supported log levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class Settings(BaseSettings):
    """Main configuration settings for InfraMaster."""
    
    # General settings
    debug: bool = Field(default=False, description="Enable debug mode")
    log_level: LogLevel = Field(default=LogLevel.INFO, description="Logging level")
    
    # LLM Configuration
    llm_provider: LLMProvider = Field(
        default=LLMProvider.VERTEX_AI,
        description="Primary LLM provider to use"
    )
    
    # Vertex AI / Google Cloud settings
    google_cloud_project: Optional[str] = Field(
        default=None,
        description="Google Cloud project ID for Vertex AI"
    )
    google_application_credentials: Optional[str] = Field(
        default=None,
        description="Path to Google Cloud service account credentials"
    )
    vertex_ai_location: str = Field(
        default="us-central1",
        description="Vertex AI location/region"
    )
    gemini_model: str = Field(
        default="gemini-1.5-pro",
        description="Gemini model to use"
    )
    
    # OpenAI settings
    openai_api_key: Optional[str] = Field(
        default=None,
        description="OpenAI API key"
    )
    openai_model: str = Field(
        default="gpt-4-turbo-preview",
        description="OpenAI model to use"
    )
    openai_base_url: Optional[str] = Field(
        default=None,
        description="Custom OpenAI API base URL"
    )
    
    # Anthropic settings
    anthropic_api_key: Optional[str] = Field(
        default=None,
        description="Anthropic API key"
    )
    anthropic_model: str = Field(
        default="claude-3-sonnet-********",
        description="Anthropic model to use"
    )
    
    # Azure OpenAI settings
    azure_openai_api_key: Optional[str] = Field(
        default=None,
        description="Azure OpenAI API key"
    )
    azure_openai_endpoint: Optional[str] = Field(
        default=None,
        description="Azure OpenAI endpoint"
    )
    azure_openai_api_version: str = Field(
        default="2024-02-15-preview",
        description="Azure OpenAI API version"
    )
    azure_openai_deployment_name: Optional[str] = Field(
        default=None,
        description="Azure OpenAI deployment name"
    )
    
    # GitHub settings
    github_token: Optional[str] = Field(
        default=None,
        description="GitHub personal access token"
    )
    github_mcp_server_url: str = Field(
        default="https://api.github.com",
        description="GitHub MCP server URL"
    )
    
    # Code Review settings
    code_review_max_files: int = Field(
        default=50,
        description="Maximum number of files to review in a single PR"
    )
    code_review_max_diff_size: int = Field(
        default=10000,
        description="Maximum diff size (lines) to review"
    )
    code_review_post_comments: bool = Field(
        default=True,
        description="Whether to post review comments to GitHub"
    )
    
    # Rate limiting
    github_rate_limit_requests: int = Field(
        default=5000,
        description="GitHub API rate limit (requests per hour)"
    )
    llm_rate_limit_requests: int = Field(
        default=100,
        description="LLM API rate limit (requests per minute)"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    @validator("google_application_credentials")
    def validate_google_credentials(cls, v):
        """Validate Google Cloud credentials path."""
        if v and not Path(v).exists():
            raise ValueError(f"Google Cloud credentials file not found: {v}")
        return v
    
    @validator("llm_provider")
    def validate_llm_provider_config(cls, v, values):
        """Validate that required settings are present for the selected LLM provider."""
        if v == LLMProvider.VERTEX_AI:
            if not values.get("google_cloud_project"):
                raise ValueError("google_cloud_project is required for Vertex AI")
        elif v == LLMProvider.OPENAI:
            if not values.get("openai_api_key"):
                raise ValueError("openai_api_key is required for OpenAI")
        elif v == LLMProvider.ANTHROPIC:
            if not values.get("anthropic_api_key"):
                raise ValueError("anthropic_api_key is required for Anthropic")
        elif v == LLMProvider.AZURE_OPENAI:
            required_fields = ["azure_openai_api_key", "azure_openai_endpoint", "azure_openai_deployment_name"]
            for field in required_fields:
                if not values.get(field):
                    raise ValueError(f"{field} is required for Azure OpenAI")
        return v


def get_settings() -> Settings:
    """Get the current settings instance."""
    return Settings()
