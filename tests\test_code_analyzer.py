"""
Tests for the code analyzer module.

Tests static code analysis functionality and issue detection.
"""

import pytest

from inframaster.core.github.models import <PERSON><PERSON><PERSON><PERSON>
from inframaster.modules.code_review.analyzer import CodeAnalyzer
from inframaster.modules.code_review.models import ReviewCategory, ReviewSeverity


class TestCodeAnalyzer:
    """Test the CodeAnalyzer class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.analyzer = CodeAnalyzer()
    
    def test_language_detection(self):
        """Test programming language detection."""
        test_cases = [
            ("main.py", "python"),
            ("app.js", "javascript"),
            ("component.tsx", "typescript"),
            ("Main.java", "java"),
            ("main.go", "go"),
            ("lib.rs", "rust"),
            ("style.css", "css"),
            ("README.md", "markdown"),
            ("Dockerfile", None),  # No extension
            ("unknown.xyz", None),  # Unknown extension
        ]
        
        for filename, expected_language in test_cases:
            detected = self.analyzer._detect_language(filename)
            assert detected == expected_language, f"Failed for {filename}"
    
    def test_generated_file_detection(self):
        """Test detection of generated files."""
        generated_files = [
            "proto_pb2.py",
            "schema.generated.ts",
            "node_modules/package/index.js",
            "vendor/lib.go",
            "build/main.js",
            "dist/bundle.min.js",
            "target/classes/Main.class",
            "package-lock.json",
        ]
        
        regular_files = [
            "main.py",
            "app.js",
            "component.tsx",
            "README.md",
        ]
        
        for filename in generated_files:
            assert self.analyzer._is_generated_file(filename), f"Should detect {filename} as generated"
        
        for filename in regular_files:
            assert not self.analyzer._is_generated_file(filename), f"Should not detect {filename} as generated"
    
    def test_python_issue_detection(self):
        """Test Python-specific issue detection."""
        test_cases = [
            # Wildcard import
            ("from module import *", "Wildcard import"),
            # Print statement
            ("print('hello world')", "Print statement found"),
            # Bare except
            ("except:", "Bare except clause"),
        ]
        
        for line, expected_title in test_cases:
            issues = self.analyzer._check_python_issues(line, 1)
            assert len(issues) > 0, f"Should detect issue in: {line}"
            assert any(expected_title in issue.title for issue in issues), f"Should find '{expected_title}' in issues"
    
    def test_javascript_issue_detection(self):
        """Test JavaScript-specific issue detection."""
        test_cases = [
            # Console statement
            ("console.log('debug')", "Console statement found"),
            # Loose equality
            ("if (x == y)", "Loose equality comparison"),
        ]
        
        for line, expected_title in test_cases:
            issues = self.analyzer._check_javascript_issues(line, 1)
            assert len(issues) > 0, f"Should detect issue in: {line}"
            assert any(expected_title in issue.title for issue in issues), f"Should find '{expected_title}' in issues"
    
    def test_common_issue_detection(self):
        """Test common issues across all languages."""
        test_cases = [
            # Long line
            ("x = " + "a" * 150, "Line too long"),
            # Trailing whitespace
            ("code_line   ", "Trailing whitespace"),
            # TODO comment
            ("# TODO: fix this later", "TODO comment found"),
            # Hardcoded password
            ("password = 'secret123'", "Potential hardcoded secret"),
            ("api_key = 'sk-1234567890'", "Potential hardcoded secret"),
        ]
        
        for line, expected_title in test_cases:
            issues = self.analyzer._check_common_issues(line, 1)
            assert len(issues) > 0, f"Should detect issue in: {line}"
            assert any(expected_title in issue.title for issue in issues), f"Should find '{expected_title}' in issues"
    
    def test_file_change_analysis(self):
        """Test analysis of a complete file change."""
        # Create a sample file change
        patch = """@@ -1,3 +1,6 @@
 def hello():
-    print("Hello")
+    print("Hello World")
+    password = "secret123"
+    # TODO: refactor this
+    except:
+        pass"""
        
        file_change = FileChange(
            filename="test.py",
            status="modified",
            additions=4,
            deletions=1,
            changes=5,
            patch=patch
        )
        
        analysis = self.analyzer.analyze_file_change(file_change)
        
        # Check basic properties
        assert analysis.file_path == "test.py"
        assert analysis.language == "python"
        assert analysis.lines_added == 4
        assert analysis.lines_removed == 1
        
        # Check that issues were detected
        assert len(analysis.issues) > 0
        
        # Check for specific issue types
        issue_titles = [issue.title for issue in analysis.issues]
        assert any("Print statement" in title for title in issue_titles)
        assert any("hardcoded secret" in title for title in issue_titles)
        assert any("TODO comment" in title for title in issue_titles)
        assert any("Bare except" in title for title in issue_titles)
        
        # Check severity distribution
        severities = [issue.severity for issue in analysis.issues]
        assert ReviewSeverity.CRITICAL in severities  # hardcoded secret
        assert ReviewSeverity.MAJOR in severities     # bare except
        assert ReviewSeverity.MINOR in severities     # print statement
        assert ReviewSeverity.INFO in severities      # TODO comment
    
    def test_complexity_calculation(self):
        """Test complexity score calculation."""
        simple_patch = """@@ -1,1 +1,2 @@
+def simple():
+    return True"""
        
        complex_patch = """@@ -1,1 +1,10 @@
+def complex_function():
+    if condition1:
+        for item in items:
+            if item.valid:
+                try:
+                    process(item)
+                except Exception:
+                    handle_error()
+            else:
+                skip_item()"""
        
        simple_score = self.analyzer._calculate_complexity(simple_patch, "python")
        complex_score = self.analyzer._calculate_complexity(complex_patch, "python")
        
        assert simple_score < complex_score
        assert 0 <= simple_score <= 10
        assert 0 <= complex_score <= 10
    
    def test_metrics_calculation(self):
        """Test code metrics calculation."""
        patch = """@@ -1,1 +1,5 @@
+# This is a comment
+def function():
+    # Another comment
+    return True
+"""
        
        metrics = self.analyzer._calculate_metrics(patch, "python")
        
        assert "total_lines" in metrics
        assert "non_empty_lines" in metrics
        assert "comment_lines" in metrics
        assert "comment_ratio" in metrics
        assert "avg_line_length" in metrics
        
        assert metrics["total_lines"] == 4
        assert metrics["comment_lines"] == 2
        assert metrics["comment_ratio"] == 0.5  # 2 comments out of 4 non-empty lines
    
    def test_skip_generated_files(self):
        """Test that generated files are skipped."""
        file_change = FileChange(
            filename="generated_pb2.py",
            status="added",
            additions=100,
            deletions=0,
            changes=100,
            patch="# Generated file content"
        )
        
        analysis = self.analyzer.analyze_file_change(file_change)
        
        # Should have basic info but no issues
        assert analysis.file_path == "generated_pb2.py"
        assert len(analysis.issues) == 0
        assert analysis.complexity_score is None
