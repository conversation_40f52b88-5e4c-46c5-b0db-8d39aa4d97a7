"""
GitHub MCP (Model Context Protocol) client implementation.

Provides integration with GitHub MCP server for enhanced
pull request analysis and code review capabilities.
"""

import json
from typing import Any, Dict, List, Optional

import httpx

from inframaster.core.github.models import GitHubError, PullRequest, ReviewComment
from inframaster.core.logging import get_logger

logger = get_logger(__name__)


class GitHubMCPClient:
    """
    GitHub MCP client for enhanced GitHub integration.
    
    This client provides additional functionality beyond the standard
    GitHub API by leveraging the GitHub MCP server.
    """
    
    def __init__(self, server_url: str, token: str):
        """
        Initialize GitHub MCP client.
        
        Args:
            server_url: MCP server URL
            token: GitHub authentication token
        """
        self.server_url = server_url.rstrip("/")
        self.token = token
        
        self.http_client = httpx.AsyncClient(
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
                "User-Agent": "InfraMaster-MCP/1.0",
            },
            timeout=60.0,
        )
        
        logger.info(f"Initialized GitHub MCP client for {server_url}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.http_client.aclose()
    
    async def get_enhanced_pr_analysis(
        self,
        owner: str,
        repo: str,
        pr_number: int,
    ) -> Dict[str, Any]:
        """
        Get enhanced PR analysis from MCP server.
        
        Args:
            owner: Repository owner
            repo: Repository name
            pr_number: Pull request number
            
        Returns:
            Enhanced analysis data including complexity metrics,
            code quality indicators, and suggested review focus areas
            
        Raises:
            GitHubError: If MCP request fails
        """
        try:
            url = f"{self.server_url}/analyze/pr"
            payload = {
                "owner": owner,
                "repo": repo,
                "pr_number": pr_number,
                "include_metrics": True,
                "include_suggestions": True,
            }
            
            response = await self.http_client.post(url, json=payload)
            response.raise_for_status()
            
            analysis = response.json()
            logger.info(f"Retrieved enhanced PR analysis for #{pr_number}")
            return analysis
            
        except httpx.HTTPStatusError as e:
            error_data = {}
            try:
                error_data = e.response.json()
            except:
                pass
            raise GitHubError(
                f"MCP server error: {e.response.status_code}",
                e.response.status_code,
                error_data
            )
        except Exception as e:
            raise GitHubError(f"MCP client error: {str(e)}")
    
    async def get_code_context(
        self,
        owner: str,
        repo: str,
        file_path: str,
        line_start: int,
        line_end: int,
        commit_sha: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get enhanced code context for a specific file range.
        
        Args:
            owner: Repository owner
            repo: Repository name
            file_path: Path to the file
            line_start: Starting line number
            line_end: Ending line number
            commit_sha: Specific commit SHA (optional)
            
        Returns:
            Code context with surrounding functions, imports,
            and related code structures
            
        Raises:
            GitHubError: If MCP request fails
        """
        try:
            url = f"{self.server_url}/context/code"
            payload = {
                "owner": owner,
                "repo": repo,
                "file_path": file_path,
                "line_start": line_start,
                "line_end": line_end,
                "commit_sha": commit_sha,
                "include_surrounding": True,
                "include_imports": True,
                "include_functions": True,
            }
            
            response = await self.http_client.post(url, json=payload)
            response.raise_for_status()
            
            context = response.json()
            logger.debug(f"Retrieved code context for {file_path}:{line_start}-{line_end}")
            return context
            
        except httpx.HTTPStatusError as e:
            error_data = {}
            try:
                error_data = e.response.json()
            except:
                pass
            raise GitHubError(
                f"MCP server error: {e.response.status_code}",
                e.response.status_code,
                error_data
            )
        except Exception as e:
            raise GitHubError(f"MCP client error: {str(e)}")
    
    async def get_similar_changes(
        self,
        owner: str,
        repo: str,
        file_path: str,
        change_type: str = "modification",
        limit: int = 5,
    ) -> List[Dict[str, Any]]:
        """
        Find similar changes in repository history.
        
        Args:
            owner: Repository owner
            repo: Repository name
            file_path: Path to the file
            change_type: Type of change ("modification", "addition", "deletion")
            limit: Maximum number of similar changes to return
            
        Returns:
            List of similar changes with commit information
            
        Raises:
            GitHubError: If MCP request fails
        """
        try:
            url = f"{self.server_url}/history/similar"
            payload = {
                "owner": owner,
                "repo": repo,
                "file_path": file_path,
                "change_type": change_type,
                "limit": limit,
                "include_context": True,
            }
            
            response = await self.http_client.post(url, json=payload)
            response.raise_for_status()
            
            similar_changes = response.json()
            logger.debug(f"Found {len(similar_changes)} similar changes for {file_path}")
            return similar_changes
            
        except httpx.HTTPStatusError as e:
            error_data = {}
            try:
                error_data = e.response.json()
            except:
                pass
            raise GitHubError(
                f"MCP server error: {e.response.status_code}",
                e.response.status_code,
                error_data
            )
        except Exception as e:
            raise GitHubError(f"MCP client error: {str(e)}")
    
    async def validate_review_suggestions(
        self,
        owner: str,
        repo: str,
        pr_number: int,
        suggestions: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Validate review suggestions against repository context.
        
        Args:
            owner: Repository owner
            repo: Repository name
            pr_number: Pull request number
            suggestions: List of review suggestions to validate
            
        Returns:
            Validation results with confidence scores and recommendations
            
        Raises:
            GitHubError: If MCP request fails
        """
        try:
            url = f"{self.server_url}/validate/suggestions"
            payload = {
                "owner": owner,
                "repo": repo,
                "pr_number": pr_number,
                "suggestions": suggestions,
                "include_confidence": True,
                "include_alternatives": True,
            }
            
            response = await self.http_client.post(url, json=payload)
            response.raise_for_status()
            
            validation = response.json()
            logger.debug(f"Validated {len(suggestions)} suggestions for PR #{pr_number}")
            return validation
            
        except httpx.HTTPStatusError as e:
            error_data = {}
            try:
                error_data = e.response.json()
            except:
                pass
            raise GitHubError(
                f"MCP server error: {e.response.status_code}",
                e.response.status_code,
                error_data
            )
        except Exception as e:
            raise GitHubError(f"MCP client error: {str(e)}")
    
    async def health_check(self) -> bool:
        """
        Check if the MCP server is healthy and accessible.
        
        Returns:
            True if server is healthy, False otherwise
        """
        try:
            url = f"{self.server_url}/health"
            response = await self.http_client.get(url)
            response.raise_for_status()
            
            health_data = response.json()
            return health_data.get("status") == "healthy"
            
        except Exception as e:
            logger.warning(f"MCP server health check failed: {e}")
            return False
