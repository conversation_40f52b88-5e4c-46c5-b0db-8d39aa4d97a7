# InfraMaster 🚀

An AI-driven toolbox for software infrastructure management, starting with intelligent code review capabilities.

## Features

### 🔍 Code Review Module
- **AI-Powered Analysis**: Leverages advanced LLMs (Gemini, GPT-4, <PERSON>) for intelligent code review
- **GitHub Integration**: Seamless PR analysis and comment posting via GitHub MCP server
- **Multi-LLM Support**: Flexible abstraction layer supporting multiple AI providers
- **Constructive Feedback**: Generates actionable suggestions and best practice recommendations

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/inframaster/inframaster.git
cd inframaster

# Install dependencies
pip install -e .

# For development
pip install -e ".[dev]"
```

### Configuration

Create a `.env` file in the project root:

```env
# LLM Configuration
LLM_PROVIDER=vertex_ai  # Options: vertex_ai, openai, anthropic, azure_openai
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# GitHub Configuration
GITHUB_TOKEN=your-github-token
GITHUB_MCP_SERVER_URL=https://api.github.com

# Optional: OpenAI Configuration
OPENAI_API_KEY=your-openai-key

# Optional: Anthropic Configuration
ANTHROPIC_API_KEY=your-anthropic-key
```

### Usage

#### CLI Interface

```bash
# Review a GitHub PR
inframaster code-review --pr-url "https://github.com/owner/repo/pull/123"

# Review with specific LLM
inframaster code-review --pr-url "https://github.com/owner/repo/pull/123" --llm openai

# Dry run (no comments posted)
inframaster code-review --pr-url "https://github.com/owner/repo/pull/123" --dry-run
```

#### Python API

```python
from inframaster.code_review import CodeReviewAgent
from inframaster.config import Settings

# Initialize the agent
settings = Settings()
agent = CodeReviewAgent(settings)

# Review a PR
result = await agent.review_pr("https://github.com/owner/repo/pull/123")
print(f"Review completed: {result.summary}")
```

## Architecture

```
inframaster/
├── core/                 # Core abstractions and utilities
│   ├── llm/             # LLM abstraction layer
│   ├── github/          # GitHub integration
│   └── config/          # Configuration management
├── modules/             # Feature modules
│   └── code_review/     # Code review module
├── cli/                 # Command-line interface
├── api/                 # REST API (future)
└── tests/               # Test suite
```

## Development

### Setup Development Environment

```bash
# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run tests
pytest

# Run linting
black .
isort .
flake8 .
mypy .
```

### Adding New Modules

InfraMaster is designed as a modular toolbox. To add a new infrastructure tool:

1. Create a new module in `inframaster/modules/`
2. Implement the module interface
3. Add CLI commands in `inframaster/cli/`
4. Update documentation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) for details.

## Roadmap

- [x] Code Review Module
- [ ] Infrastructure Monitoring
- [ ] Deployment Automation
- [ ] Security Scanning
- [ ] Performance Analysis
- [ ] Documentation Generation
