"""
Basic usage examples for InfraMaster code review.

This script demonstrates how to use InfraMaster programmatically
for AI-powered code review of GitHub pull requests.
"""

import asyncio
import os
from inframaster.core.config import Settings
from inframaster.modules.code_review import CodeReviewAgent
from inframaster.modules.code_review.models import ReviewConfig


async def basic_code_review():
    """Basic code review example."""
    
    # Load settings from environment
    settings = Settings()
    
    # Create a custom review configuration
    config = ReviewConfig(
        max_files_to_review=20,
        focus_on_security=True,
        focus_on_performance=True,
        post_comments=False,  # Don't post comments in this example
    )
    
    # Initialize the code review agent
    agent = CodeReviewAgent(settings, config)
    
    # Review a pull request
    pr_url = "https://github.com/owner/repo/pull/123"
    
    try:
        result = await agent.review_pr(
            pr_url=pr_url,
            dry_run=True,  # Don't post comments
        )
        
        print(f"Review completed!")
        print(f"Files analyzed: {len(result.files_analyzed)}")
        print(f"Total issues: {result.total_issues}")
        print(f"Overall score: {result.overall_score:.1f}/10")
        print(f"Recommendation: {result.recommendation}")
        
        # Print critical issues
        critical_issues = result.critical_issues
        if critical_issues:
            print(f"\nCritical issues found:")
            for issue in critical_issues:
                print(f"  - {issue.title} ({issue.file_path}:{issue.line_number})")
        
        return result
        
    except Exception as e:
        print(f"Error during code review: {e}")
        return None


async def custom_llm_provider():
    """Example using a specific LLM provider."""
    
    # Create settings with specific LLM provider
    settings = Settings(
        llm_provider="openai",  # Use OpenAI instead of default
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        openai_model="gpt-4-turbo-preview",
    )
    
    # Custom configuration for security-focused review
    config = ReviewConfig(
        focus_on_security=True,
        focus_on_performance=False,
        focus_on_style=False,
        min_confidence_threshold=0.8,  # Higher confidence threshold
    )
    
    agent = CodeReviewAgent(settings, config)
    
    pr_url = "https://github.com/owner/repo/pull/456"
    
    result = await agent.review_pr(pr_url, dry_run=True)
    
    # Focus on security issues
    security_issues = [
        issue for file_analysis in result.files_analyzed
        for issue in file_analysis.issues
        if issue.category.value == "security"
    ]
    
    print(f"Security issues found: {len(security_issues)}")
    for issue in security_issues:
        print(f"  - {issue.title}: {issue.description}")


async def batch_review():
    """Example of reviewing multiple PRs."""
    
    settings = Settings()
    agent = CodeReviewAgent(settings)
    
    pr_urls = [
        "https://github.com/owner/repo/pull/123",
        "https://github.com/owner/repo/pull/124", 
        "https://github.com/owner/repo/pull/125",
    ]
    
    results = []
    
    for pr_url in pr_urls:
        try:
            print(f"Reviewing {pr_url}...")
            result = await agent.review_pr(pr_url, dry_run=True)
            results.append(result)
            print(f"  - {result.total_issues} issues found")
        except Exception as e:
            print(f"  - Error: {e}")
    
    # Summary
    total_issues = sum(r.total_issues for r in results)
    avg_score = sum(r.overall_score or 0 for r in results) / len(results)
    
    print(f"\nBatch review summary:")
    print(f"PRs reviewed: {len(results)}")
    print(f"Total issues: {total_issues}")
    print(f"Average score: {avg_score:.1f}/10")


if __name__ == "__main__":
    # Run basic example
    print("Running basic code review example...")
    asyncio.run(basic_code_review())
    
    print("\n" + "="*50 + "\n")
    
    # Run custom LLM provider example
    print("Running custom LLM provider example...")
    asyncio.run(custom_llm_provider())
    
    print("\n" + "="*50 + "\n")
    
    # Run batch review example
    print("Running batch review example...")
    asyncio.run(batch_review())
