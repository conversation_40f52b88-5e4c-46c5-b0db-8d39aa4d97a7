"""
Tests for configuration management.

Tests the Settings class and configuration validation.
"""

import os
import tempfile
from pathlib import Path

import pytest
from pydantic import ValidationError

from inframaster.core.config import LL<PERSON>rovider, Settings


class TestSettings:
    """Test the Settings configuration class."""
    
    def test_default_settings(self):
        """Test default settings creation."""
        settings = Settings()
        
        assert settings.llm_provider == LLMProvider.VERTEX_AI
        assert settings.debug is False
        assert settings.log_level.value == "INFO"
        assert settings.vertex_ai_location == "us-central1"
        assert settings.gemini_model == "gemini-1.5-pro"
    
    def test_vertex_ai_validation(self):
        """Test Vertex AI configuration validation."""
        # Should fail without project ID
        with pytest.raises(ValidationError):
            Settings(
                llm_provider=LLMProvider.VERTEX_AI,
                google_cloud_project=None
            )
        
        # Should succeed with project ID
        settings = Settings(
            llm_provider=LLMProvider.VERTEX_AI,
            google_cloud_project="test-project"
        )
        assert settings.google_cloud_project == "test-project"
    
    def test_openai_validation(self):
        """Test OpenAI configuration validation."""
        # Should fail without API key
        with pytest.raises(ValidationError):
            Settings(
                llm_provider=LLMProvider.OPENAI,
                openai_api_key=None
            )
        
        # Should succeed with API key
        settings = Settings(
            llm_provider=LLMProvider.OPENAI,
            openai_api_key="test-key"
        )
        assert settings.openai_api_key == "test-key"
    
    def test_anthropic_validation(self):
        """Test Anthropic configuration validation."""
        # Should fail without API key
        with pytest.raises(ValidationError):
            Settings(
                llm_provider=LLMProvider.ANTHROPIC,
                anthropic_api_key=None
            )
        
        # Should succeed with API key
        settings = Settings(
            llm_provider=LLMProvider.ANTHROPIC,
            anthropic_api_key="test-key"
        )
        assert settings.anthropic_api_key == "test-key"
    
    def test_azure_openai_validation(self):
        """Test Azure OpenAI configuration validation."""
        # Should fail without required fields
        with pytest.raises(ValidationError):
            Settings(
                llm_provider=LLMProvider.AZURE_OPENAI,
                azure_openai_api_key=None
            )
        
        # Should succeed with all required fields
        settings = Settings(
            llm_provider=LLMProvider.AZURE_OPENAI,
            azure_openai_api_key="test-key",
            azure_openai_endpoint="https://test.openai.azure.com/",
            azure_openai_deployment_name="gpt-4"
        )
        assert settings.azure_openai_api_key == "test-key"
        assert settings.azure_openai_endpoint == "https://test.openai.azure.com/"
    
    def test_credentials_file_validation(self):
        """Test Google Cloud credentials file validation."""
        # Should fail with non-existent file
        with pytest.raises(ValidationError):
            Settings(
                google_application_credentials="/non/existent/file.json"
            )
        
        # Should succeed with existing file
        with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as f:
            temp_file = Path(f.name)
            
        try:
            settings = Settings(
                google_application_credentials=str(temp_file)
            )
            assert settings.google_application_credentials == str(temp_file)
        finally:
            temp_file.unlink()
    
    def test_env_file_loading(self):
        """Test loading settings from environment file."""
        # Create temporary .env file
        env_content = """
DEBUG=true
LOG_LEVEL=DEBUG
LLM_PROVIDER=openai
OPENAI_API_KEY=test-key-from-env
GITHUB_TOKEN=test-github-token
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
            f.write(env_content)
            env_file = Path(f.name)
        
        try:
            settings = Settings(_env_file=str(env_file))
            
            assert settings.debug is True
            assert settings.log_level.value == "DEBUG"
            assert settings.llm_provider == LLMProvider.OPENAI
            assert settings.openai_api_key == "test-key-from-env"
            assert settings.github_token == "test-github-token"
        finally:
            env_file.unlink()
    
    def test_environment_variable_override(self):
        """Test that environment variables override defaults."""
        # Set environment variable
        os.environ["DEBUG"] = "true"
        os.environ["LLM_PROVIDER"] = "anthropic"
        os.environ["ANTHROPIC_API_KEY"] = "env-anthropic-key"
        
        try:
            settings = Settings()
            
            assert settings.debug is True
            assert settings.llm_provider == LLMProvider.ANTHROPIC
            assert settings.anthropic_api_key == "env-anthropic-key"
        finally:
            # Clean up environment variables
            os.environ.pop("DEBUG", None)
            os.environ.pop("LLM_PROVIDER", None)
            os.environ.pop("ANTHROPIC_API_KEY", None)
