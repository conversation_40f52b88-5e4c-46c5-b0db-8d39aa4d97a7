[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "inframaster"
version = "0.1.0"
description = "AI-driven toolbox for software infrastructure management"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "InfraMaster Team", email = "<EMAIL>"}
]
keywords = ["ai", "infrastructure", "code-review", "automation", "devops"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Quality Assurance",
    "Topic :: Software Development :: Version Control :: Git",
]

dependencies = [
    "langchain>=0.1.0",
    "langchain-community>=0.0.20",
    "langchain-google-vertexai>=1.0.0",
    "langchain-openai>=0.0.8",
    "langchain-anthropic>=0.1.0",
    "google-cloud-aiplatform>=1.40.0",
    "openai>=1.10.0",
    "anthropic>=0.18.0",
    "requests>=2.31.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "click>=8.1.0",
    "rich>=13.7.0",
    "python-dotenv>=1.0.0",
    "PyGithub>=2.1.0",
    "gitpython>=3.1.40",
    "tenacity>=8.2.0",
    "structlog>=23.2.0",
    "httpx>=0.26.0",
    "fastapi>=0.108.0",
    "uvicorn>=0.25.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.12.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
]

[project.urls]
Homepage = "https://github.com/inframaster/inframaster"
Repository = "https://github.com/inframaster/inframaster"
Documentation = "https://inframaster.readthedocs.io"
"Bug Tracker" = "https://github.com/inframaster/inframaster/issues"

[project.scripts]
inframaster = "inframaster.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["inframaster*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --cov=inframaster --cov-report=term-missing --cov-report=html"

[tool.coverage.run]
source = ["inframaster"]
omit = ["*/tests/*", "*/test_*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
