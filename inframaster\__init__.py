"""
InfraMaster - AI-driven toolbox for software infrastructure management

A modular toolbox for automating and enhancing software infrastructure tasks
using artificial intelligence and modern development practices.

Features:
- AI-powered code review for GitHub pull requests
- Multi-LLM support (Vertex AI, OpenAI, Anthropic, Azure OpenAI)
- Flexible architecture for adding new infrastructure tools
- Command-line and programmatic interfaces
"""

__version__ = "0.1.0"
__author__ = "InfraMaster Team"
__email__ = "<EMAIL>"

# Core imports
from inframaster.core.config import Settings, get_settings

# Module imports
from inframaster.modules.code_review import CodeReviewAgent

__all__ = [
    "Settings",
    "get_settings",
    "CodeReviewAgent",
]
