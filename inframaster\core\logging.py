"""
Logging configuration for InfraMaster.

Provides structured logging with rich formatting and proper log levels.
"""

import logging
import sys
from typing import Optional

import structlog
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from inframaster.core.config import LogLevel, Settings


def setup_logging(settings: Optional[Settings] = None) -> None:
    """
    Set up structured logging for InfraMaster.
    
    Args:
        settings: Configuration settings. If None, will create default settings.
    """
    if settings is None:
        from inframaster.core.config import get_settings
        settings = get_settings()
    
    # Configure standard library logging
    logging.basicConfig(
        level=getattr(logging, settings.log_level.value),
        format="%(message)s",
        datefmt="[%X]",
        handlers=[
            RichHandler(
                console=Console(stderr=True),
                show_time=True,
                show_path=True,
                markup=True,
                rich_tracebacks=True,
            )
        ],
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.processors.add_log_level,
            structlog.processors.StackInfoRenderer(),
            structlog.dev.set_exc_info,
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.dev.ConsoleRenderer(colors=True),
        ],
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.log_level.value)
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)
