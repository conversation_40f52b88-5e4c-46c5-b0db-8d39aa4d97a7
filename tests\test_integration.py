"""
Integration tests for InfraMaster.

Tests the complete workflow from configuration to code review.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from inframaster.core.config import Settings, LLMProvider
from inframaster.core.github.models import <PERSON>Change, PullRequest, GitHubUser, GitHubRepository
from inframaster.modules.code_review.agent import CodeReviewAgent
from inframaster.modules.code_review.models import ReviewConfig


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    return Settings(
        llm_provider=LLMProvider.OPENAI,
        openai_api_key="test-key",
        github_token="test-github-token",
        debug=True,
    )


@pytest.fixture
def mock_pull_request():
    """Create a mock pull request for testing."""
    user = GitHubUser(
        login="testuser",
        id=123,
        avatar_url="https://github.com/testuser.png",
        html_url="https://github.com/testuser",
    )
    
    repo = GitHubRepository(
        id=456,
        name="test-repo",
        full_name="testuser/test-repo",
        owner=user,
        html_url="https://github.com/testuser/test-repo",
        clone_url="https://github.com/testuser/test-repo.git",
        default_branch="main",
    )
    
    file_change = FileChange(
        filename="test.py",
        status="modified",
        additions=5,
        deletions=2,
        changes=7,
        patch="""@@ -1,3 +1,6 @@
 def hello():
-    print("Hello")
+    print("Hello World")
+    password = "secret123"
+    # TODO: refactor this
+    return True"""
    )
    
    return PullRequest(
        id=789,
        number=1,
        title="Test PR",
        body="This is a test pull request",
        state="open",
        html_url="https://github.com/testuser/test-repo/pull/1",
        diff_url="https://github.com/testuser/test-repo/pull/1.diff",
        patch_url="https://github.com/testuser/test-repo/pull/1.patch",
        user=user,
        base_repo=repo,
        head_repo=repo,
        base_ref="main",
        head_ref="feature-branch",
        base_sha="abc123",
        head_sha="def456",
        created_at="2024-01-01T00:00:00Z",
        updated_at="2024-01-01T01:00:00Z",
        additions=5,
        deletions=2,
        changed_files=1,
        commits=1,
        files=[file_change],
    )


class TestCodeReviewIntegration:
    """Integration tests for the code review workflow."""
    
    @pytest.mark.asyncio
    async def test_complete_review_workflow(self, mock_settings, mock_pull_request):
        """Test the complete code review workflow."""
        
        # Mock LLM response
        mock_llm_response = MagicMock()
        mock_llm_response.content = """{
            "issues": [
                {
                    "line_number": 3,
                    "severity": "critical",
                    "category": "security",
                    "title": "Hardcoded password detected",
                    "description": "Password is hardcoded in the source code",
                    "suggestion": "Use environment variables for sensitive data",
                    "confidence": 0.9
                }
            ],
            "complexity_score": 3.5,
            "summary": "Found security issue with hardcoded password",
            "recommendations": ["Use environment variables", "Add input validation"]
        }"""
        
        # Mock the LLM provider
        mock_llm_provider = AsyncMock()
        mock_llm_provider.provider_name = "openai"
        mock_llm_provider.model_name = "gpt-4-turbo-preview"
        mock_llm_provider.generate.return_value = mock_llm_response
        
        # Mock GitHub client
        mock_github_client = AsyncMock()
        mock_github_client.parse_pr_url.return_value = ("testuser", "test-repo", 1)
        mock_github_client.get_pull_request.return_value = mock_pull_request
        mock_github_client.create_review.return_value = MagicMock()
        
        with patch('inframaster.core.llm.factory.LLMFactory.create_provider', return_value=mock_llm_provider), \
             patch('inframaster.core.github.client.GitHubClient', return_value=mock_github_client):
            
            # Create agent and run review
            config = ReviewConfig(post_comments=False)  # Don't post comments in test
            agent = CodeReviewAgent(mock_settings, config)
            
            result = await agent.review_pr(
                pr_url="https://github.com/testuser/test-repo/pull/1",
                dry_run=True
            )
            
            # Verify results
            assert result is not None
            assert result.pr_number == 1
            assert result.repository == "testuser/test-repo"
            assert result.reviewer_provider == "openai"
            assert result.reviewer_model == "gpt-4-turbo-preview"
            
            # Check that files were analyzed
            assert len(result.files_analyzed) == 1
            file_analysis = result.files_analyzed[0]
            assert file_analysis.file_path == "test.py"
            assert file_analysis.language == "python"
            
            # Check that issues were found
            assert result.total_issues > 0
            
            # Verify LLM was called
            mock_llm_provider.generate.assert_called()
            
            # Verify GitHub client was used
            mock_github_client.get_pull_request.assert_called_with("testuser", "test-repo", 1)
    
    @pytest.mark.asyncio
    async def test_error_handling(self, mock_settings):
        """Test error handling in the review workflow."""
        
        # Mock GitHub client that raises an error
        mock_github_client = AsyncMock()
        mock_github_client.parse_pr_url.side_effect = ValueError("Invalid PR URL")
        
        with patch('inframaster.core.github.client.GitHubClient', return_value=mock_github_client):
            
            agent = CodeReviewAgent(mock_settings)
            
            # Should raise the error
            with pytest.raises(ValueError, match="Invalid PR URL"):
                await agent.review_pr("invalid-url")
    
    @pytest.mark.asyncio
    async def test_dry_run_mode(self, mock_settings, mock_pull_request):
        """Test that dry run mode doesn't post comments."""
        
        # Mock LLM provider
        mock_llm_provider = AsyncMock()
        mock_llm_provider.provider_name = "openai"
        mock_llm_provider.model_name = "gpt-4-turbo-preview"
        mock_llm_provider.generate.return_value = MagicMock(content='{"issues": [], "summary": "No issues"}')
        
        # Mock GitHub client
        mock_github_client = AsyncMock()
        mock_github_client.parse_pr_url.return_value = ("testuser", "test-repo", 1)
        mock_github_client.get_pull_request.return_value = mock_pull_request
        
        with patch('inframaster.core.llm.factory.LLMFactory.create_provider', return_value=mock_llm_provider), \
             patch('inframaster.core.github.client.GitHubClient', return_value=mock_github_client):
            
            agent = CodeReviewAgent(mock_settings)
            
            result = await agent.review_pr(
                pr_url="https://github.com/testuser/test-repo/pull/1",
                dry_run=True
            )
            
            # Verify that no comments were posted
            mock_github_client.create_review.assert_not_called()
            mock_github_client.post_review_comment.assert_not_called()
            
            # But the review should still be completed
            assert result is not None
            assert result.pr_number == 1
    
    def test_configuration_validation(self):
        """Test configuration validation."""
        
        # Valid configuration
        settings = Settings(
            llm_provider=LLMProvider.OPENAI,
            openai_api_key="test-key",
            github_token="test-token"
        )
        
        agent = CodeReviewAgent(settings)
        assert agent.settings.llm_provider == LLMProvider.OPENAI
        
        # Test custom review config
        config = ReviewConfig(
            max_files_to_review=10,
            focus_on_security=True,
            post_comments=False
        )
        
        agent_with_config = CodeReviewAgent(settings, config)
        assert agent_with_config.config.max_files_to_review == 10
        assert agent_with_config.config.focus_on_security is True
        assert agent_with_config.config.post_comments is False
