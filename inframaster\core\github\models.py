"""
Data models for GitHub integration.

Defines Pydantic models for GitHub API responses and data structures.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field, HttpUrl


class GitHubUser(BaseModel):
    """GitHub user information."""
    login: str
    id: int
    avatar_url: HttpUrl
    html_url: HttpUrl
    type: str = "User"


class GitHubRepository(BaseModel):
    """GitHub repository information."""
    id: int
    name: str
    full_name: str
    owner: GitHubUser
    html_url: HttpUrl
    clone_url: HttpUrl
    default_branch: str = "main"
    private: bool = False


class FileChange(BaseModel):
    """Represents a file change in a pull request."""
    filename: str
    status: str  # "added", "modified", "removed", "renamed"
    additions: int = 0
    deletions: int = 0
    changes: int = 0
    patch: Optional[str] = None
    previous_filename: Optional[str] = None


class PullRequest(BaseModel):
    """GitHub pull request information."""
    id: int
    number: int
    title: str
    body: Optional[str] = None
    state: str  # "open", "closed", "merged"
    html_url: HttpUrl
    diff_url: HttpUrl
    patch_url: HttpUrl
    
    # User information
    user: GitHubUser
    assignees: List[GitHubUser] = []
    requested_reviewers: List[GitHubUser] = []
    
    # Repository information
    base_repo: GitHubRepository
    head_repo: GitHubRepository
    base_ref: str
    head_ref: str
    base_sha: str
    head_sha: str
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    merged_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    
    # Statistics
    additions: int = 0
    deletions: int = 0
    changed_files: int = 0
    commits: int = 0
    
    # Files changed
    files: List[FileChange] = []
    
    # Labels and metadata
    labels: List[str] = []
    draft: bool = False
    mergeable: Optional[bool] = None
    mergeable_state: Optional[str] = None
    
    @property
    def is_mergeable(self) -> bool:
        """Check if the PR is mergeable."""
        return self.mergeable is True and self.mergeable_state == "clean"
    
    @property
    def total_changes(self) -> int:
        """Get total number of line changes."""
        return self.additions + self.deletions


class ReviewComment(BaseModel):
    """GitHub review comment."""
    id: Optional[int] = None
    body: str
    path: str
    line: Optional[int] = None
    side: str = "RIGHT"  # "LEFT" or "RIGHT"
    start_line: Optional[int] = None
    start_side: Optional[str] = None
    
    # For existing comments
    user: Optional[GitHubUser] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    html_url: Optional[HttpUrl] = None
    
    # Position in diff (for new comments)
    position: Optional[int] = None
    original_position: Optional[int] = None
    
    # Commit SHA
    commit_id: Optional[str] = None
    original_commit_id: Optional[str] = None


class Review(BaseModel):
    """GitHub pull request review."""
    id: Optional[int] = None
    user: Optional[GitHubUser] = None
    body: Optional[str] = None
    state: str  # "PENDING", "APPROVED", "CHANGES_REQUESTED", "COMMENTED"
    html_url: Optional[HttpUrl] = None
    submitted_at: Optional[datetime] = None
    commit_id: Optional[str] = None
    
    # Comments in this review
    comments: List[ReviewComment] = []


class GitHubError(Exception):
    """GitHub API error."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data or {}


class RateLimitError(GitHubError):
    """GitHub API rate limit exceeded."""
    
    def __init__(self, reset_time: Optional[datetime] = None):
        super().__init__("GitHub API rate limit exceeded")
        self.reset_time = reset_time
